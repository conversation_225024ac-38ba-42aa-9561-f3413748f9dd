FROM python:3.10-slim

# ติดตั้ง ffmpeg และ git
RUN apt-get update && apt-get install -y ffmpeg git && rm -rf /var/lib/apt/lists/*

# สร้างโฟลเดอร์ทำงาน
WORKDIR /app

# คัดลอก requirements.txt และติดตั้งก่อน (cache-friendly)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# คัดลอกไฟล์ทั้งหมดที่เหลือ
COPY . .

# รันแอป
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "9100"]

