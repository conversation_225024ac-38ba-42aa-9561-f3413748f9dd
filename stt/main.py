# whisper/main.py
from fastapi import FastAPI, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
import whisper
import os
import tempfile

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
model = whisper.load_model("small")  # หรือ "base", "medium", "large"

@app.post("/stt")
async def transcribe_audio(file: UploadFile = File(...)):
    try:
        # สร้างไฟล์ชั่วคราว
        with tempfile.NamedTemporaryFile(delete=False, suffix=".webm") as tmp:
            tmp.write(await file.read())
            tmp_path = tmp.name

        # ใช้ whisper แปลงเสียง
        result = model.transcribe(tmp_path, language="th")

        # ลบไฟล์
        os.remove(tmp_path)

        return {"text": result["text"]}
    except Exception as e:
        return {"error": str(e)}