# Changelog

## [1.0.0] - 2024-01-01

### Added

- **API Gateway**: สร้าง API Gateway ด้วย Rust สำหรับจัดการ request ไปยัง backend services
- **Service Routing**: ระบบ routing ที่ส่ง request ไปยัง services ต่างๆ ตาม path
- **Authentication**: JWT-based authentication system
- **Rate Limiting**: ระบบจำกัดจำนวน request ต่อ IP
- **Health Check**: ระบบตรวจสอบสถานะของ backend services
- **CORS Support**: รองรับ Cross-Origin Resource Sharing
- **Logging**: ระบบ logging ที่ครบถ้วน
- **Retry Logic**: ลองใหม่เมื่อ request ล้มเหลว
- **Docker Support**: พร้อมใช้งานใน Docker container

### Changed

- **Webspeak Configuration**: อัพเดท webspeak service ให้ใช้ API Gateway แทนการเรียก services โดยตรง
- **Docker Compose**: เพิ่ม API Gateway service ใน docker-compose.yml หลัก
- **Service URLs**: เปลี่ยนการเรียก services ผ่าน API Gateway

### Technical Details

#### API Gateway Features

- **Framework**: Axum (Rust)
- **Port**: 8080
- **Services Supported**:
  - RAG Service (`/api/rag/*`)
  - Qwen Service (`/api/qwen/*`)
  - Embed Service (`/api/embed/*`)
  - STT Service (`/api/stt/*`)
  - TTS Service (`/api/tts/*`)

#### Configuration

- **Environment Variables**: รองรับการตั้งค่าผ่าน environment variables
- **Config File**: รองรับการตั้งค่าผ่าน `config.toml`
- **Health Check**: `/health` endpoint สำหรับตรวจสอบสถานะ

#### Security

- **JWT Authentication**: รองรับ Bearer token authentication
- **Rate Limiting**: จำกัด request ต่อ IP (ปรับได้)
- **CORS**: รองรับ Cross-Origin requests

#### Monitoring

- **Logging**: Structured logging ด้วย tracing
- **Health Monitoring**: ตรวจสอบสถานะของทุก services
- **Error Handling**: ระบบจัดการ error ที่ครบถ้วน

### Migration Guide

#### สำหรับ Frontend (Webspeak)

เปลี่ยนการเรียก API จาก:

```javascript
// เดิม
const response = await fetch("http://rag:9000/ask", {
  method: "POST",
  body: JSON.stringify({ question: "คำถาม" }),
});

// ใหม่
const response = await fetch("http://api-gateway:8080/api/rag/ask", {
  method: "POST",
  body: JSON.stringify({ question: "คำถาม" }),
});
```

#### สำหรับ Backend Services

ไม่ต้องเปลี่ยนแปลงใดๆ เนื่องจาก API Gateway จะ forward request ไปยัง services เดิม

### Deployment

#### การ Deploy ใหม่

```bash
# รันทั้งหมดพร้อม API Gateway
docker-compose up --build

# หรือรันเฉพาะ API Gateway
cd gateway
docker-compose up --build
```

#### การตรวจสอบ

```bash
# ตรวจสอบ health
curl http://localhost:8080/health

# ทดสอบ API
curl -X POST http://localhost:8080/api/rag/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "ทดสอบ"}'
```

### Breaking Changes

- ไม่มี breaking changes สำหรับ backend services
- Frontend ต้องอัพเดท URL ให้ใช้ API Gateway

### Future Enhancements

- [ ] Metrics และ Monitoring Dashboard
- [ ] API Documentation (OpenAPI/Swagger)
- [ ] Circuit Breaker Pattern
- [ ] Load Balancing
- [ ] API Versioning
- [ ] Request/Response Transformation
- [ ] API Key Management
- [ ] OAuth2 Integration
