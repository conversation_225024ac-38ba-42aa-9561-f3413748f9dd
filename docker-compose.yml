# docker-compose.yml
services:
  # API Gateway - จุดเข้าใช้งานหลัก
  api-gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - RUST_LOG=info
      - SERVER_PORT=8080
      - SERVER_HOST=0.0.0.0
      - SERVICES_RAG_URL=http://rag:9000
      - SERVICES_QWEN_URL=http://qwen:8000
      - SERVICES_EMBED_URL=http://embed:8001
      - SERVICES_STT_URL=http://stt:9100
      - SERVICES_TTS_URL=http://tts:4000
      - AUTH_JWT_SECRET=${JWT_SECRET:-your-secret-key-change-in-production}
      - AUTH_TOKEN_EXPIRY=3600
      - RATE_LIMIT_REQUESTS_PER_MINUTE=100
      - RATE_LIMIT_BURST_SIZE=10
    depends_on:
      - rag
      - qwen
      - embed
      - stt
      - tts
    restart: unless-stopped
    networks:
      - sky-ass-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  cloudflared:
    image: cloudflare/cloudflared:latest
    restart: unless-stopped
    command: tunnel run
    environment:
      - TUNNEL_TOKEN=${TUNNEL_TOKEN}
    networks:
      - sky-ass-network
  # ใช้ cloudflare tunnel แทน nginx
  # nginx:
  #   build: ./nginx
  #   ports:
  #     - "80:80"
  #   depends_on:
  #     - webspeak
  #     - rag
  #     - embed
  #     - tts
  #     - weaviate
  #   networks:
  #     - sky-ass-network
  stt:
    build: ./stt
    ports:
      - "9100:9100"
    restart: unless-stopped
    networks:
      - sky-ass-network
  webspeak:
    build: ./webspeak
    ports:
      - "3000:3000"
    environment:
      - SERVER_IP=${SERVER_IP:-localhost}
      - API_GATEWAY_URL=http://api-gateway:8080
      - TTS_URL=http://api-gateway:8080/api/tts
      - QWEN_URL=http://api-gateway:8080/api/qwen
      - RAG_URL=http://api-gateway:8080/api/rag
      - STT_URL=http://api-gateway:8080/api/stt
    depends_on:
      - api-gateway
    restart: unless-stopped
    networks:
      - sky-ass-network

  rag:
    build:
      context: ./rag
      dockerfile: Dockerfile
    ports:
      - "9000:9000"
    environment:
      - WEAVIATE_URL=http://weaviate:8080
      - EMBED_API_URL=http://embed:8001
      - QWEN_URL=http://host.docker.internal:8000/chat
    volumes:
      - ./rag/data:/app/data
    depends_on:
      - weaviate
    restart: unless-stopped
    networks:
      - sky-ass-network

  weaviate:
    image: semitechnologies/weaviate:1.23.3
    ports:
      - "8080:8080"
    environment:
      - QUERY_DEFAULTS_LIMIT=25
      - AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true
      - PERSISTENCE_DATA_PATH=/var/lib/weaviate
      - DEFAULT_VECTORIZER_MODULE=none
      - ENABLE_MODULES=
      - CLUSTER_HOSTNAME=node1
      - EMBED_API_URL=http://embed-api:8001/embed
      - RAW_DATA_PATH=./data
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - sky-ass-network

  embed:
    build:
      context: ./embed
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - EMBED_MODEL=all-MiniLM-L6-v2
      - CORS_ORIGINS=http://localhost:9000
    restart: unless-stopped
    networks:
      - sky-ass-network

  qwen:
    build:
      context: ./qwen
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - MODEL_NAME=Qwen/Qwen2.5-7B-Instruct
      - MAX_LENGTH=2048
      - TEMPERATURE=0.7
    restart: unless-stopped
    networks:
      - sky-ass-network

  tts:
    build:
      context: ./tts
    ports:
      - "4000:4000"
    volumes:
      - ./tts:/app
    environment:
      - SERVER_IP=${SERVER_IP:-localhost}
    restart: unless-stopped
    networks:
      - sky-ass-network

networks:
  sky-ass-network:
    external: true

volumes:
  weaviate_data:
