FROM nvidia/cuda:11.7.1-cudnn8-runtime-ubuntu20.04
WORKDIR /app

# Install pip and upgrade
RUN apt-get update && apt-get install -y python3-pip && \
    pip install --upgrade pip

# Install GPU-accelerated PyTorch + CUDA
RUN pip install --no-cache-dir \
    torch==2.0.1+cu117 \
    torchvision==0.15.2+cu117 \
    --extra-index-url https://download.pytorch.org/whl/cu117

# Install other Python deps
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy app code and expose port
COPY main.py .
EXPOSE 8001

# Start server
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001", "--workers", "2"]