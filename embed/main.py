import os
import time
import logging
from typing import List

import torch
from fastapi import FastAPI, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from pydantic import BaseModel, Field
from sentence_transformers import SentenceTransformer

# Logging Configuration
logging.basicConfig(
    format="%(asctime)s %(levelname)s %(name)s — %(message)s",
    level=logging.INFO,
)
logger = logging.getLogger("embed-api")

# Environment & Configuration
MODEL_NAME      = os.getenv("EMBED_MODEL", "all-MiniLM-L6-v2")
RAW_ORIGINS     = os.getenv("CORS_ORIGINS", "http://localhost:9000")
MAX_BATCH       = int(os.getenv("MAX_BATCH_SIZE", 50))
DEVICE          = "cuda" if torch.cuda.is_available() else "cpu"
PORT            = int(os.getenv("PORT", 8001))
UVICORN_WORKERS = int(os.getenv("UVICORN_WORKERS", 2))
logger.info(f"Loading model '{MODEL_NAME}' on device '{DEVICE}'")

# Model Initialization
model = SentenceTransformer(
    MODEL_NAME,
    device=DEVICE,
    trust_remote_code=True
)

# FastAPI Application
app = FastAPI(
    title="Embed-API",
    version="1.0",
    description="High-performance embedding service with GPU acceleration"
)

# Middleware: CORS & GZip & Timing
origins = [o.strip() for o in RAW_ORIGINS.split(",") if o.strip()]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=500)

@app.middleware("http")
async def add_process_time(request: Request, call_next):
    start = time.time()
    response: Response = await call_next(request)
    elapsed = (time.time() - start) * 1000
    response.headers["X-Process-Time-Ms"] = f"{elapsed:.2f}"
    logger.info(f"{request.method} {request.url.path} — {elapsed:.2f}ms")
    return response

# Schemas
class EmbedRequest(BaseModel):
    texts: List[str] = Field(
        ..., min_items=1, max_items=MAX_BATCH,
        description=f"Batch of texts (1 to {MAX_BATCH} items)"
    )
class EmbedResponse(BaseModel):
    vectors: List[List[float]]
class HealthResponse(BaseModel):
    status: str

# Endpoints
@app.get("/health", response_model=HealthResponse)
async def health():
    return {"status": "ok"}

@app.post("/embed", response_model=EmbedResponse)
async def embed_text(request: EmbedRequest):
    try:
        embeddings = model.encode(
            request.texts,
            convert_to_numpy=True,
            show_progress_bar=False,
            normalize_embeddings=True
        )
        return {"vectors": embeddings.tolist()}
    except Exception:
        logger.exception("Embedding failed for %d texts", len(request.texts))
        raise HTTPException(status_code=500, detail="Internal embedding error")