from fastapi import <PERSON><PERSON><PERSON>
from pydantic import BaseModel
from transformers import AutoTokenizer, AutoModelForCausalLM
from fastapi.middleware.cors import CORSMiddleware
import torch
import re
import os

app = FastAPI()

# <lemmaค่าจาก environment variables
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:9000")
MODEL_NAME = os.getenv("MODEL_NAME", "Qwen/Qwen3-0.6B")
MAX_NEW_TOKENS = int(os.getenv("MAX_NEW_TOKENS", "2048"))
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.7"))
TOP_P = float(os.getenv("TOP_P", "0.9"))
REPETITION_PENALTY = float(os.getenv("REPETITION_PENALTY", "1.1"))


app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

print(f"🔧 Using model: {M<PERSON>EL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    trust_remote_code=True,
    device_map="auto",
    torch_dtype="auto"
)

class ChatRequest(BaseModel):
    messages: list  # [{"role": "user", "content": "..."}]

@app.get("/")
def root():
    return {"message": "Qwen3 is running!", "model": MODEL_NAME}

def clean_text(text):
    """
    ลบอักษรและอักขระ<lemma่ไม่ต้องการออกจากข้อความ
    """
    # ลบอักษร ยกเว้นอักษร, เลข, อักษรภาษาไทย, สระ, วรรณกต์ และช่องว่าง
    cleaned_text = re.sub(r"[^a-zA-Z0-9ก-ฮะ-์\s]", "", text)
    # ลบช่องว่างซ้ำ
    cleaned_text = re.sub(r"\s+", " ", cleaned_text).strip()
    return cleaned_text

@app.post("/chat")
def chat(req: ChatRequest):
    try:
        print("📥 Received request:", req.messages)  # Log ข้อมูล<lemma่ได้จาก request

        # สร้าง prompt
        prompt = tokenizer.apply_chat_template(
            req.messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=True
        )
        print("📝 Generated prompt:", prompt)  # Log prompt <lemma่สร้าง

        # เตรียม input โมเดล
        inputs = tokenizer([prompt], return_tensors="pt").to(model.device)
        print("📦 Tokenized inputs:", inputs)  # Log input <lemma่<lemmaไว้<lemmaโมเดล

        # เรียกโมเดลเพื่อสร้างคำตอบ
        outputs = model.generate(
            **inputs,
            max_new_tokens=MAX_NEW_TOKENS,
            temperature=TEMPERATURE,
            top_p=TOP_P,
            do_sample=True,
            repetition_penalty=REPETITION_PENALTY,
            eos_token_id=tokenizer.eos_token_id,
            pad_token_id=tokenizer.pad_token_id
        )
        print("🔮 Model outputs:", outputs)  # Log output <lemma่ได้จากโมเดล

        # ลบ prompt เ<lemmaมออกจาก output
        output_text = tokenizer.decode(
            outputs[0][inputs["input_ids"].shape[1]:],
            skip_special_tokens=True
        ).strip()
        print("✂️ Decoded output text:", output_text)  # Log ข้อความ<lemma่ถอดแล้ว

        # แยกเนื้อหาคำตอบออกจาก <think> ถ้า<lemma
        if "</think>" in output_text:
            _, answer = output_text.split("</think>", 1)
            answer = answer.strip()
        else:
            answer = output_text
        print("💬 Final answer before cleaning:", answer)  # Log คำตอบก่อนทำความสะอาด

        # ลบอักษรออกจากคำตอบ
        cleaned_answer = clean_text(answer)
        print("🧹 Cleaned answer:", cleaned_answer)  # Log คำตอบ<lemma่ทำความสะอาด

        return {"reply": cleaned_answer}

    except Exception as e:
        print("🔥 ERROR:", str(e))  # Log ข้อ<lemmaพลาด
        return {"error": str(e)}
