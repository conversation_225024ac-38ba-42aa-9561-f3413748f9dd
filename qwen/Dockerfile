FROM python:3.10

WORKDIR /app

RUN pip install --upgrade pip && \
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 && \
    pip install transformers fastapi uvicorn accelerate

# ตรวจสอบว่า app.py อยู่ในโฟลเดอร์แล้ว
COPY app.py .
COPY requirements.txt .
RUN pip install -r requirements.txt

# เซ็ต environment variables
ENV HOST=0.0.0.0
ENV PORT=8000
ENV SERVER_IP=${SERVER_IP:-localhost}

# เปิด port
EXPOSE 8000

CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"]