services:
  qwen:
    build: . # ใช้ Dockerfile ในโฟลเดอร์
    # runtime: nvidia  # ถ้าต้องการใช้ GPU ให้เอา comment ออก
    # environment:
    #   - HOST=0.0.0.0
    #   - PORT=8000
    #   - SERVER_IP=${SERVER_IP:-localhost}
      # - NVIDIA_VISIBLE_DEVICES=all  # ถ้าใช้ GPU
      # - HF_TOKEN=your_huggingface_token  # ถ้าจำเป็น
    ports:
      - "8000:8000"
    volumes:
      - ./models:/app/models # ถ้าโฟลเดอร์ modelsเก็บโมเดล
    networks:
      - sky-ass-network
networks:
  sky-ass-network:
    external: true