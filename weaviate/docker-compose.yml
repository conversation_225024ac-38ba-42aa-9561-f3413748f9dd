# weaviate/docker-compose.yml
services:
  weaviate:
    image: semitechnologies/weaviate:1.23.3
    ports:
      - "8080:8080"
    environment:
      - QUERY_DEFAULTS_LIMIT=25
      - AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true
      - PERSISTENCE_DATA_PATH=/var/lib/weaviate
      - DEFAULT_VECTORIZER_MODULE=none
      - ENABLE_MODULES=
      - CLUSTER_HOSTNAME=node1
      - EMBED_API_URL=http://embed-api:8001/embed
      - RAW_DATA_PATH=./data
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - sky-ass-network

networks:
  sky-ass-network:
    external: true
