[package]
name = "api-gateway"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = "0.7.9"
tokio = { version = "1.47.0", features = ["full"] }

# HTTP client
reqwest = { version = "0.11.27", features = ["json"] }

# Serialization
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.141"

# Configuration
config = "0.14.1"
dotenv = "0.15.0"

# Logging
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }

# Error handling
anyhow = "1.0.98"
thiserror = "1.0.69"

# Authentication
jsonwebtoken = "9.3.1"

# Rate limiting
governor = "0.6.3"

# Utilities
uuid = { version = "1.17.0", features = ["v4"] }
chrono = { version = "0.4.31", features = ["serde"] }

# Async traits
async-trait = "0.1.88"

# HTTP middleware
tower = "0.4.13"
tower-http = { version = "0.5.2", features = ["cors", "compression-full", "trace"] }

# HTTP types
http = "0.2.12" 