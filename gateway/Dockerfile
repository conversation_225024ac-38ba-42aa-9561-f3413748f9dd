# Use the official Rust image as a builder
FROM rust:latest as builder

# Set working directory
WORKDIR /usr/src/app

# Copy the manifests
COPY Cargo.toml ./

# Create a dummy main.rs to build dependencies
RUN mkdir src && echo "fn main() {}" > src/main.rs

# Build dependencies
RUN cargo build --release

# Remove the dummy main.rs and copy the real source code
RUN rm src/main.rs
COPY src ./src

# Build the application
RUN cargo build --release

# Create a new stage with a minimal image
FROM debian:bookworm-slim

# Install ca-certificates for HTTPS requests
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*

# Create a non-root user
RUN useradd -r -s /bin/false app

# Set working directory
WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /usr/src/app/target/release/api-gateway .

# Copy configuration files
COPY config.toml ./

# Change ownership to the app user
RUN chown -R app:app /app

# Switch to the app user
USER app

# Expose port
EXPOSE 8080

# Set environment variables
ENV RUST_LOG=info

# Run the binary
CMD ["./api-gateway"] 