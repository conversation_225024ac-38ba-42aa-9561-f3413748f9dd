services:
  api-gateway:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: api-gateway
    ports:
      - "8080:8080"
    environment:
      - RUST_LOG=info
      - SERVER_PORT=8080
      - SERVER_HOST=0.0.0.0
      - SERVICES_RAG_URL=http://rag:8000
      - SERVICES_QWEN_URL=http://qwen:8000
      - SERVICES_EMBED_URL=http://embed:8000
      - SERVICES_STT_URL=http://stt:8000
      - SERVICES_TTS_URL=http://tts:8000
      - AUTH_JWT_SECRET=your-secret-key-change-in-production
      - AUTH_TOKEN_EXPIRY=3600
      - RATE_LIMIT_REQUESTS_PER_MINUTE=100
      - RATE_LIMIT_BURST_SIZE=10
    networks:
      - api-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  api-network:
    driver: bridge
