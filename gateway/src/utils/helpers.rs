use std::collections::HashMap;
use axum::http::HeaderMap;

pub fn convert_headers(headers: HeaderMap) -> HashMap<String, String> {
    let mut headers_map = HashMap::new();
    for (key, value) in headers.iter() {
        let key_str = key.as_str();
        if let Ok(value_str) = value.to_str() {
            if !matches!(key_str.to_lowercase().as_str(), "host" | "content-length") {
                headers_map.insert(key_str.to_string(), value_str.to_string());
            }
        }
    }
    headers_map
}
