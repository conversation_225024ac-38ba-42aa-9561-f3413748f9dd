use axum::{extract::Request, http::HeaderMap, middleware::Next, response::Response};
use jsonwebtoken::{decode, DecodingKey, Validation};
use tracing::{info, warn};
use crate::{config::AppConfig, error::AppError, models::JwtClaims};

pub async fn auth_middleware(
    headers: HeaderMap,
    request: Request,
    next: Next,
    config: std::sync::Arc<AppConfig>
) -> Result<Response, AppError> {
    let auth_header = headers
        .get("Authorization")
        .and_then(|h| h.to_str().ok())
        .and_then(|h| h.strip_prefix("Bearer "));

    if let Some(token) = auth_header {
        match validate_jwt(token, &config.auth.jwt_secret) {
            Ok(claims) => {
                info!("Authenticated user: {}", claims.username);
                Ok(next.run(request).await)
            }
            Err(_) => {
                warn!("Invalid JWT token");
                Err(AppError::Unauthorized("Invalid token".to_string()))
            }
        }
    } else {
        info!("No authentication token provided");
        Ok(next.run(request).await)
    }
}

fn validate_jwt(token: &str, secret: &str) -> Result<JwtClaims, AppError> {
    let token_data = decode::<JwtClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &Validation::default()
    ).map_err(|_| AppError::Unauthorized("Invalid token".to_string()))?;
    
    Ok(token_data.claims)
}
