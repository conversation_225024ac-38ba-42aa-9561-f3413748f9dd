use axum::{extract::Request, http::HeaderMap, middleware::Next, response::Response};
use governor::{Quota, RateLimiter, state::InMemoryState, clock::Default<PERSON><PERSON>, state::NotKeyed};
use std::{num::NonZeroU32, sync::Arc};
use tracing::{info, warn};
use crate::{config::AppConfig, error::AppError};

pub type RateLimiterState = Arc<RateLimiter<NotKeyed, InMemoryState, DefaultClock>>;

pub async fn rate_limit_middleware(headers: HeaderMap, request: Request, next: Next, rate_limiter: RateLimiterState) -> Result<Response, AppError> {
    let client_ip = headers.get("X-Forwarded-For").or(headers.get("X-Real-IP")).and_then(|h| h.to_str().ok()).unwrap_or("unknown");
    if rate_limiter.check().is_ok() {
        info!("Rate limit check passed for IP: {}", client_ip);
        Ok(next.run(request).await)
    } else {
        warn!("Rate limit exceeded for IP: {}", client_ip);
        Err(AppError::RateLimitExceeded)
    }
}

pub fn create_rate_limiter(config: &AppConfig) -> RateLimiterState {
    let quota = Quota::per_minute(NonZeroU32::new(config.rate_limit.requests_per_minute).unwrap())
        .allow_burst(NonZeroU32::new(config.rate_limit.burst_size).unwrap());
    Arc::new(RateLimiter::direct(quota))
}
