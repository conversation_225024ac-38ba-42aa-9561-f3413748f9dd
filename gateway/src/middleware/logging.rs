use axum::{extract::Request, middleware::Next, response::Response};
use std::time::Instant;
use tracing::info;
use crate::error::AppError;

pub async fn logging_middleware(
    request: Request,
    next: Next
) -> Result<Response, AppError> {
    let start = Instant::now();
    let method = request.method().clone();
    let uri = request.uri().clone();
    let user_agent = request
        .headers()
        .get("User-Agent")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("unknown");

    info!("Request: {} {} - User-Agent: {}", method, uri, user_agent);

    let response = next.run(request).await;
    let duration = start.elapsed();

    info!(
        "Response: {} {} - Status: {} - Duration: {:?}",
        method, uri, response.status(), duration
    );

    Ok(response)
}
