use axum::Router;
use std::sync::Arc;
use tower_http::cors::CorsLayer;
use crate::{
    config::AppConfig, 
    error::AppError, 
    handlers::{AppState, health_handler, proxy_handler, rag_handler, qwen_handler, embed_handler, stt_handler, tts_handler},
    services::ServiceProxy
};

pub async fn create_router(config: AppConfig) -> Result<Router, AppError> {
    let config = Arc::new(config);
    let service_proxy = Arc::new(ServiceProxy::new(config.clone()));
    
    let app_state = Arc::new(AppState {
        config: config.clone(),
        service_proxy,
        rate_limiter: Arc::new(governor::RateLimiter::direct(
            governor::Quota::per_minute(std::num::NonZeroU32::new(100).unwrap())
        )),
    });

    let cors = CorsLayer::permissive();

    let app = Router::new()
        .route("/health", axum::routing::get(health_handler))
        .route("/api/:service/*path", axum::routing::any(proxy_handler))
        .route("/api/rag", axum::routing::any(rag_handler))
        .route("/api/qwen", axum::routing::any(qwen_handler))
        .route("/api/embed", axum::routing::any(embed_handler))
        .route("/api/stt", axum::routing::any(stt_handler))
        .route("/api/tts", axum::routing::any(tts_handler))
        .layer(cors)
        .with_state(app_state);

    Ok(app)
}
