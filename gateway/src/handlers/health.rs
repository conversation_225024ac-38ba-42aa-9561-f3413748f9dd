use axum::{extract::State, response::<PERSON><PERSON>};
use std::{collections::HashMap, sync::Arc};
use tracing::info;
use crate::{error::AppError, models::{ApiResponse, HealthCheck}};
use super::AppState;

pub async fn health_handler(
    State(state): State<Arc<AppState>>,
) -> Result<Json<ApiResponse<HealthCheck>>, AppError> {
    info!("Health check requested");

    let mut services = HashMap::new();
    let service_names = vec!["rag", "qwen", "embed", "stt", "tts"];

    for service_name in service_names {
        let health = state.service_proxy.check_service_health(service_name).await;
        services.insert(service_name.to_string(), health);
    }

    let health_check = HealthCheck {
        status: "healthy".to_string(),
        timestamp: chrono::Utc::now().to_rfc3339(),
        services,
    };

    Ok(Json(ApiResponse::success(health_check)))
}
