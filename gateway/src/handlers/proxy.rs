use axum::{extract::{Path, State}, http::{HeaderMap, Method}, response::<PERSON><PERSON>};
use serde_json::Value;
use std::{collections::HashMap, sync::Arc};
use tracing::{error, info};
use crate::{
    error::AppError, 
    models::{ApiResponse, ServiceRequest, ServiceResponse},
    services::base::BackendService
};
use super::AppState;

pub async fn proxy_handler(
    State(state): State<Arc<AppState>>,
    Path((service, path)): Path<(String, String)>,
    method: Method,
    headers: HeaderMap,
    body: Option<Json<Value>>
) -> Result<Json<ApiResponse<ServiceResponse>>, AppError> {
    info!("Proxying request to service: {} path: {}", service, path);

    let mut headers_map = HashMap::new();
    for (key, value) in headers.iter() {
        let key_str = key.as_str();
        if let Ok(value_str) = value.to_str() {
            if !matches!(key_str.to_lowercase().as_str(), "host" | "content-length") {
                headers_map.insert(key_str.to_string(), value_str.to_string());
            }
        }
    }

    let service_request = ServiceRequest {
        service,
        endpoint: format!("/{}", path),
        method: method.to_string(),
        headers: Some(headers_map),
        body: body.map(|b| b.0),
    };

    match state.service_proxy.forward_request(service_request).await {
        Ok(response) => {
            info!("Successfully forwarded request");
            Ok(Json(ApiResponse::success(response)))
        }
        Err(e) => {
            error!("Failed to forward request: {:?}", e);
            Err(e)
        }
    }
}
