use axum::{extract::State, http::{HeaderMap, Method}, response::<PERSON><PERSON>};
use serde_json::Value;
use std::{collections::HashMap, sync::Arc};
use tracing::{error, info};
use crate::{
    error::AppError, 
    models::{ApiResponse, ServiceRequest, ServiceResponse},
    services::base::BackendService
};
use super::AppState;

pub async fn tts_handler(
    State(state): State<Arc<AppState>>,
    method: Method,
    headers: HeaderMap,
    body: Option<Json<Value>>
) -> Result<Json<ApiResponse<ServiceResponse>>, AppError> {
    info!("TTS service request");

    let service_request = ServiceRequest {
        service: "tts".to_string(),
        endpoint: "/".to_string(),
        method: method.to_string(),
        headers: Some(convert_headers(headers)),
        body: body.map(|b| b.0),
    };

    match state.service_proxy.forward_request(service_request).await {
        Ok(response) => {
            info!("Successfully forwarded TTS request");
            Ok(Json(ApiResponse::success(response)))
        }
        Err(e) => {
            error!("Failed to forward TTS request: {:?}", e);
            Err(e)
        }
    }
}

fn convert_headers(headers: HeaderMap) -> HashMap<String, String> {
    let mut headers_map = HashMap::new();
    for (key, value) in headers.iter() {
        let key_str = key.as_str();
        if let Ok(value_str) = value.to_str() {
            if !matches!(key_str.to_lowercase().as_str(), "host" | "content-length") {
                headers_map.insert(key_str.to_string(), value_str.to_string());
            }
        }
    }
    headers_map
}
