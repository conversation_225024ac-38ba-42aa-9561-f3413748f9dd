use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub services: ServicesConfig,
    pub auth: AuthConfig,
    pub rate_limit: RateLimitConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct ServicesConfig {
    pub rag: ServiceConfig,
    pub qwen: ServiceConfig,
    pub embed: ServiceConfig,
    pub stt: ServiceConfig,
    pub tts: ServiceConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    pub url: String,
    pub retries: u32,
    pub timeout: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthConfig {
    pub jwt_secret: String,
    pub token_expiry: u64,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct RateLimitConfig {
    pub requests_per_minute: u32,
    pub burst_size: u32,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            server: ServerConfig {
                host: "0.0.0.0".to_string(),
                port: 3000,
            },
            services: ServicesConfig {
                rag: ServiceConfig {
                    url: "http://rag:8000".to_string(),
                    retries: 3,
                    timeout: 30,
                },
                qwen: ServiceConfig {
                    url: "http://qwen:8000".to_string(),
                    retries: 3,
                    timeout: 30,
                },
                embed: ServiceConfig {
                    url: "http://embed:8000".to_string(),
                    retries: 3,
                    timeout: 30,
                },
                stt: ServiceConfig {
                    url: "http://stt:8000".to_string(),
                    retries: 3,
                    timeout: 30,
                },
                tts: ServiceConfig {
                    url: "http://tts:8000".to_string(),
                    retries: 3,
                    timeout: 30,
                },
            },
            auth: AuthConfig {
                jwt_secret: "your-secret-key".to_string(),
                token_expiry: 3600,
            },
            rate_limit: RateLimitConfig {
                requests_per_minute: 100,
                burst_size: 10,
            },
        }
    }
}

impl AppConfig {
    pub fn load() -> Result<Self, config::ConfigError> {
        let builder = config::Config::builder()
            .set_default("server.host", "0.0.0.0")?
            .set_default("server.port", 3000)?
            .set_default("services.rag.url", "http://rag:8000")?
            .set_default("services.rag.retries", 3)?
            .set_default("services.rag.timeout", 30)?
            .set_default("services.qwen.url", "http://qwen:8000")?
            .set_default("services.qwen.retries", 3)?
            .set_default("services.qwen.timeout", 30)?
            .set_default("services.embed.url", "http://embed:8000")?
            .set_default("services.embed.retries", 3)?
            .set_default("services.embed.timeout", 30)?
            .set_default("services.stt.url", "http://stt:8000")?
            .set_default("services.stt.retries", 3)?
            .set_default("services.stt.timeout", 30)?
            .set_default("services.tts.url", "http://tts:8000")?
            .set_default("services.tts.retries", 3)?
            .set_default("services.tts.timeout", 30)?
            .set_default("auth.jwt_secret", "your-secret-key")?
            .set_default("auth.token_expiry", 3600)?
            .set_default("rate_limit.requests_per_minute", 100)?
            .set_default("rate_limit.burst_size", 10)?;

        // Load from environment variables
        let builder = builder.add_source(config::Environment::default().separator("__"));

        // Load from config file if it exists
        let builder = if std::path::Path::new("config.toml").exists() {
            builder.add_source(config::File::with_name("config"))
        } else {
            builder
        };

        builder.build()?.try_deserialize()
    }
} 