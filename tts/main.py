# tts/main.py
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from gtts import gTTS
from dotenv import load_dotenv
import os
import uuid

# โหลด .env
load_dotenv()

# อ่านค่าจาก environment
AUDIO_OUTPUT_DIR = os.getenv("AUDIO_OUTPUT_DIR", "audio-input")
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://localhost:5173,https://aivoices.work")

# แปลง CORS_ORIGINS เป็น list
origin_list = [origin.strip() for origin in CORS_ORIGINS.split(",") if origin.strip()]

# เตรียมโฟลเดอร์เก็บไฟล์เสียง
os.makedirs(AUDIO_OUTPUT_DIR, exist_ok=True)

# สร้าง FastAPI app
app = FastAPI()

# ตั้งค่า CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=origin_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# รูปแบบ request
class TTSRequest(BaseModel):
    text: str
    lang: str = "th"
    slow: bool = False

# Endpoint หลัก
@app.post("/tts")
async def tts(request: TTSRequest):
    if not request.text.strip():
        raise HTTPException(status_code=400, detail="Text is empty")

    filename = f"{uuid.uuid4().hex}.mp3"
    filepath = os.path.join(AUDIO_OUTPUT_DIR, filename)

    print(f"Generating TTS for: {request.text}, lang: {request.lang}, slow: {request.slow}")
    
    try:
        tts = gTTS(text=request.text, lang=request.lang, slow=request.slow)
        tts.save(filepath)
        return FileResponse(filepath, media_type="audio/mpeg", filename=filename)
    except Exception as e:
        print(f"Error generating TTS: {str(e)}")
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")
