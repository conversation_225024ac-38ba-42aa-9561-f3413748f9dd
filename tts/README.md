# Text-to-Speech Docker Project

This project provides a simple text-to-speech (TTS) conversion service using Docker. It utilizes a Python script to convert text input into an audio file.

## Getting Started

### Prerequisites

- Docker
- Docker Compose

### Installation

1. Clone the repository:

   ```
   git clone <repository-url>
   cd tts-docker-project
   ```

2. Build the Docker container:
   ```
   docker-compose build
   ```

### Running the Application

To run the TTS service, use the following command:

```
docker-compose up
```

### Usage

Once the service is running, you can send a request to convert text to speech. You can modify the `src/main.py` file to customize the input text and output audio file name.

### Stopping the Service

To stop the service, press `CTRL+C` in the terminal where the Docker container is running, or use:

```
docker-compose down
```

## License

This project is licensed under the MIT License.
