# nginx/nginx.conf
server {
    listen 80;

    server_name localhost;

    # Frontend (SvelteKit, Webspeak)
    location / {
        proxy_pass http://webspeak:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # API - RAG
    location /api/rag/ {
        proxy_pass http://rag-ap:9000/;
        proxy_set_header Host $host;
    }

    # API - Embed
    location /api/embed/ {
        proxy_pass http://embed:8001/;
        proxy_set_header Host $host;
    }

    # API - TTS
    location /api/tts/ {
        proxy_pass http://tts:4000/;
        proxy_set_header Host $host;
    }

    # Weaviate UI หรือ admin
    location /weaviate/ {
        proxy_pass http://weaviate:8080/;
        proxy_set_header Host $host;
    }
}
