# main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.ingest_router import router as ingest_router
from app.api.ask_router import router as ask_router
from app.api.delete_router import router as delete_router
from app.api.document_router import router as document_router
from dotenv import load_dotenv

load_dotenv()
app = FastAPI(title="RAG Service")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.include_router(ingest_router)
app.include_router(ask_router)
app.include_router(delete_router)
app.include_router(document_router)