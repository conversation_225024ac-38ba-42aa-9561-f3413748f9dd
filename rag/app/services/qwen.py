import re
from openai import OpenAI
from app.config import DASHSCOPE_API_KEY, QWEN_MODEL

client = OpenAI(api_key=DASHSCOPE_API_KEY, base_url="https://dashscope-intl.aliyuncs.com/compatible-mode/v1")

def ask_qwen(prompt: str, lang: str) -> str:
    messages = [
        {"role": "system", "content": f"ตอบกลับเป็นภาษา ตาม code ภาษาของ lang: {lang} เท่านั้น"},
        {"role": "user", "content": prompt}
    ]
    completion = client.chat.completions.create(model=QWEN_MODEL, messages=messages)
    return completion.choices[0].message.content

def clean_text_for_tts(text: str) -> str:
    text = re.sub(r'\.(?!\s|$)', '. ', text)
    text = re.sub(r"[^\w\sก-๙.,!?()'\"-]", '', text)
    text = re.sub(r'\s+', ' ', text).strip()
    return text