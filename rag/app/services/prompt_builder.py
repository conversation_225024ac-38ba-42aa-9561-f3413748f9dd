# prompt_builder.py
def build_prompt(documents: list[dict], question: str):
    print("🟡 [BUILD_PROMPT] Received question:", question)
    
    if not documents:
        print("🔴 [BUILD_PROMPT] ❌ No documents retrieved.")
        context = "[No relevant context found]"
    else:
        print(f"🟢 [BUILD_PROMPT] ✅ Retrieved {len(documents)} documents")
        for i, d in enumerate(documents):
            print(f"  📄 Doc {i+1}: source={d.get('source')} page={d.get('page')} chunk={d.get('chunk')} section={d.get('section')} subsection={d.get('subsection')}")
        
        context = "\n".join(
            f"[{d['source']}|p{d['page']}|c{d['chunk']}|{d.get('section', '')}/{d.get('subsection', '')}] {d['text']}"
            for d in documents
        )
        print("📝 [BUILD_PROMPT] Context preview:", context[:300], "...")

    prompt = f"Context:\n{context}\n\nQuestion: {question}\nAnswer:"
    return prompt, context

