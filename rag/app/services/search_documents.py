import re, requests, weaviate
from typing import List
from app.config import WEAVIATE_URL, EMBED_API_URL

client = weaviate.Client(WEAVIATE_URL)



def search_documents(vector: List[float], filters: dict = None) -> list:
    query = (
        client.query
              .get("Document", ["text", "source", "page", "chunk", "filetype", "section", "subsection"])
              .with_near_vector({"vector": vector, "certainty": 0.5})
              .with_limit(8)
    )

    # ถ้ามี filter เช่น {"section": "ทักษะความสามารถ"} หรือ {"filetype": "pdf"}
    if filters:
        where_clause = {
            "operator": "And",
            "operands": []
        }

        for key, value in filters.items():
            where_clause["operands"].append({
                "path": [key],
                "operator": "Equal",
                "valueString": value
            })

        query = query.with_where(where_clause)

    result = query.do()
    return result.get("data", {}).get("Get", {}).get("Document", [])


