# services/parsers/parsers_json.py
import json

def parsers_json(path: str) -> list[dict]:
    with open(path, encoding="utf-8") as f:
        data = json.load(f)
    if isinstance(data, list):
        return [{"text": d.get("text") or d.get("content") or "", "metadata": d} for d in data]
    elif isinstance(data, dict):
        return [{"text": data.get("text") or data.get("content") or "", "metadata": data}]
    return []