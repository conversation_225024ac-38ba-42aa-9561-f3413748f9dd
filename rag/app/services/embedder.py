
# embedder.py
import requests
from fastapi import HTTPException
from ..config import EMBED_API_URL

def embed_text(text: str) -> list[float]:
    try:
        endpoint = EMBED_API_URL.rstrip("/")
        if not endpoint.endswith("/embed"):
            endpoint += "/embed"
        resp = requests.post(endpoint, json={"texts": [text]})
        resp.raise_for_status()
        vector = resp.json()["vectors"][0]
        return vector
    except Exception as e:
        raise HTTPException(status_code=502, detail=f"Embedding service error: {e}")





    # try:
    #     endpoint = EMBED_API_URL.rstrip("/")
    #     if not endpoint.endswith("/embed"):
    #         endpoint += "/embed"
    #     resp = requests.post(endpoint, json={"texts": [req.speak]})
    #     resp.raise_for_status()
    #     vector = resp.json()["vectors"][0]
    # except Exception as e:
    #     raise HTTPException(status_code=502, detail=f"Embedding service error: {e}")