# app/services/delete_manager.py
import weaviate
from app.config import WEAVIATE_URL

client = weaviate.Client(WEAVIATE_URL)

def delete_by_id(object_id: str, class_name: str):
    client.data_object.delete(uuid=object_id, class_name=class_name)

def delete_all_by_class(class_name: str, batch_size: int) -> int:
    total = 0
    while True:
        res = client.query.get(class_name, ["_additional { id }"]).with_limit(batch_size).do()
        items = res.get("data", {}).get("Get", {}).get(class_name, [])
        if not items:
            break
        for item in items:
            client.data_object.delete(uuid=item["_additional"]["id"], class_name=class_name)
            total += 1
    return total

def delete_by_source(filename: str, class_name: str = "Document") -> int:
    where_filter = {
        "path": ["source"],
        "operator": "Equal",
        "valueString": filename
    }
    result = client.query.get(class_name, ["_additional { id }"]).with_where(where_filter).do()
    deleted = 0
    for obj in result.get("data", {}).get("Get", {}).get(class_name, []):
        obj_id = obj["_additional"]["id"]
        client.data_object.delete(uuid=obj_id, class_name=class_name)
        deleted += 1
    return deleted