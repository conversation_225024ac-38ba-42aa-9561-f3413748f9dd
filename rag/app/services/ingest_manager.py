import os
import hashlib
import logging
from datetime import datetime
from app.utils.chunker import chunk_text
from app.config import RAW_DATA_PATH, WEAVIATE_URL
from app.services.embedder import embed_text
from app.models.weaviate_schema import DOCUMENT_CLASS_SCHEMA
from app.services.delete_manager import delete_by_source
from app.services.parsers.parsers_pdf import parsers_pdf
from app.services.parsers.parsers_docx import parsers_docx
from app.services.parsers.parsers_csv import parsers_csv
from app.services.parsers.parsers_txt import parsers_txt
from app.services.parsers.parsers_json import parsers_json
import weaviate

# Initialize Weaviate client
client = weaviate.Client(WEAVIATE_URL)

# Setup logger
logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(message)s")
logger = logging.getLogger(__name__)

def create_schema_if_not_exists():
    schema = client.schema.get().get('classes', [])
    if not any(cls.get('class') == 'Document' for cls in schema):
        client.schema.create_class(DOCUMENT_CLASS_SCHEMA)
        logger.info("Weaviate schema for 'Document' created.")

def reset_schema():
    schema = client.schema.get().get('classes', [])
    if any(cls.get('class') == 'Document' for cls in schema):
        client.schema.delete_class('Document')
        logger.info("Weaviate schema for 'Document' reset.")

def embed_and_store_chunks(chunks, metadata, batch):
    count = 0
    for i, chunk in enumerate(chunks):
        try:
            vec = embed_text(chunk)
            uid = hashlib.md5(f"{metadata['source']}_{metadata.get('page', 0)}_{i}".encode()).hexdigest()
            batch.add_data_object({
                **metadata,
                "chunk": i,
                "text": chunk
            }, "Document", uuid=uid, vector=vec)
            count += 1
        except Exception as e:
            logger.exception(f"[ERROR] Embedding or storing chunk failed: {metadata['source']} chunk {i}")
    return count

def parse_file(path: str):
    ext = os.path.splitext(path)[-1].lower().strip('.')
    parser_map = {
        'pdf': parsers_pdf,
        'docx': parsers_docx,
        'doc': parsers_docx,
        'csv': parsers_csv,
        'txt': parsers_txt,
        'json': parsers_json
    }
    if ext not in parser_map:
        raise ValueError(f"Unsupported file type: {ext}")
    return parser_map[ext](path), ext

def ingest_file(path: str) -> int:
    create_schema_if_not_exists()
    filename = os.path.basename(path)
    delete_by_source(filename)

    try:
        records, ext = parse_file(path)
    except Exception as e:
        logger.error(f"[ERROR] Failed to parse file: {path} | {e}")
        return 0

    batch = client.batch
    batch.batch_size = 32

    count = 0
    for i, record in enumerate(records):
        chunks = chunk_text(record.get("text", ""))
        meta_raw = record.get("metadata", {})
        metadata = {
            "source": filename,
            "page": meta_raw.get("page", i),
            "filetype": ext,
            "fullpath": path,
            "ingested_at": datetime.utcnow().isoformat(),
            "section": meta_raw.get("section", ""),
            "subsection": meta_raw.get("subsection", "")  # 👈 เพิ่มตรงนี้
        }
        count += embed_and_store_chunks(chunks, metadata, batch)

    batch.create_objects()
    logger.info(f"[INGEST] {filename}: {count} chunks stored")
    return count

def ingest_all_documents(force_schema_reset=False) -> int:
    if force_schema_reset:
        reset_schema()
    create_schema_if_not_exists()

    total_count = 0
    for root, _, files in os.walk(RAW_DATA_PATH):
        for filename in files:
            full_path = os.path.join(root, filename)
            total_count += ingest_file(full_path)

    logger.info(f"[INGEST ALL] Completed: {total_count} chunks stored from all documents")
    return total_count

def ingest_since(timestamp: datetime) -> int:
    count = 0
    for root, _, files in os.walk(RAW_DATA_PATH):
        for filename in files:
            path = os.path.join(root, filename)
            mtime = datetime.utcfromtimestamp(os.path.getmtime(path))
            if mtime > timestamp:
                count += ingest_file(path)
    logger.info(f"[INGEST SINCE] {count} chunks stored from updated files")
    return count
