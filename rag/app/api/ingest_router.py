# app/api/ingest_router.py
from fastapi import APIRouter, HTTPException, UploadFile, File
from app.services.ingest_manager import ingest_all_documents, ingest_file, ingest_since
from app.models.schema import IngestResponse, FilenameRequest, IngestSinceRequest
from typing import Optional
import os
from datetime import datetime
from app.config import RAW_DATA_PATH

router = APIRouter(prefix="/ingest", tags=["ingest"])

@router.post("/filename", response_model=IngestResponse)
async def ingest_from_existing_filename(req: FilenameRequest):
    try:
        path = os.path.join(RAW_DATA_PATH, req.filename)
        if not os.path.isfile(path):
            raise HTTPException(
                status_code=404,
                detail=f"404: File '{path}' not found. Please provide relative path from RAW_DATA_PATH."
            )
        count = ingest_file(path)
        return IngestResponse(status="success", count=count)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("", response_model=IngestResponse)
async def ingest_all():
    try:
        count = ingest_all_documents()
        return IngestResponse(status="success", count=count)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/file", response_model=IngestResponse)
async def ingest_from_file(file: UploadFile = File(...)):
    try:
        temp_path = os.path.join(RAW_DATA_PATH, file.filename)
        with open(temp_path, "wb") as f:
            f.write(file.file.read())
        count = ingest_file(temp_path)
        return IngestResponse(status="success", count=count)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/since", response_model=IngestResponse)
async def ingest_since_timestamp(req: IngestSinceRequest):
    try:
        ts = datetime.fromisoformat(req.iso_datetime)
        count = ingest_since(ts)
        return IngestResponse(status="success", count=count)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid datetime format. Use ISO 8601 (e.g. 2024-06-01T12:00:00)")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))