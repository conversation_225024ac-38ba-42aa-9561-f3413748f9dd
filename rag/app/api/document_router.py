# app/api/document_router.py
from fastapi import APIRouter, HTTPException
import weaviate
from app.config import WEAVIATE_URL

router = APIRouter(prefix="/documents", tags=["documents"])
client = weaviate.Client(WEAVIATE_URL)

@router.get("/")
async def list_documents():
    class_name = "Document"  # ✅ สามารถเปลี่ยนหรือ loop หลาย class ได้ในอนาคต
    try:
        result = (
            client.query
                .get(class_name, ["source", "page", "chunk", "_additional { id }"])
                .with_limit(100)
                .do()
        )
        raw_documents = result.get("data", {}).get("Get", {}).get(class_name, [])
        
        # ✅ เพิ่ม class_name ลงไปในแต่ละ document
        documents = [
            {
                **doc,
                "class_name": class_name
            }
            for doc in raw_documents
        ]

        return documents

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch documents: {e}")
