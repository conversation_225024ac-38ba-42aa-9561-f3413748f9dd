import ast
import os
import re
import json
import requests
import weaviate
import logging

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from ..config import WEAVIATE_URL, EMBED_API_URL, QWEN_URL, DASHSCOPE_API_KEY, <PERSON><PERSON><PERSON>_MODEL
from openai import OpenAI
from app.models.schema import AskRequest, AskResponse
from app.services.embedder import embed_text
from app.services.search_documents import search_documents
from app.services.prompt_builder import build_prompt
from app.services.qwen import ask_qwen, clean_text_for_tts

# ตั้งค่าระบบ logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s"
)

qwen_client = OpenAI(
    api_key=DASHSCOPE_API_KEY,
    base_url="https://dashscope-intl.aliyuncs.com/compatible-mode/v1"
)

weaviate_client = weaviate.Client(WEAVIATE_URL)

router = APIRouter()

@router.post("/ask")
async def ask(req: AskRequest):
    # 1. ฝังข้อความเป็นเวกเตอร์
    try:
        vector = embed_text(req.speak)
      
    except Exception as e:
        logging.error(f"[❌ EMBED ERROR] {e}")
        raise HTTPException(status_code=502, detail=f"Embedding service error: {e}")

    # 2. ค้นหาเอกสารที่ใกล้เคียง
    try:
        # # ไม่กรองใด ๆ
        # search_documents(my_vector)

        # # กรองเฉพาะ section
        # search_documents(my_vector, filters={"section": "ทักษะความสามารถ"})

        # # กรอง filetype และ subsection
        # search_documents(my_vector, filters={
        #     "filetype": "json",
        #     "subsection": "สร้าง frontend"
        # })
        
        docs = search_documents(vector)
  
    except Exception as e:
        logging.error(f"[❌ WEAVIATE ERROR] {e}")
        raise HTTPException(status_code=500, detail=f"Weaviate search error: {e}")

    # 3. สร้าง prompt
    try:
        prompt, context = build_prompt(docs, req.speak)
    except Exception as e:
        logging.error(f"[❌ PROMPT ERROR] {e}")
        raise HTTPException(status_code=500, detail=f"Prompt building error: {e}")

    # 4. ส่ง prompt ไปที่ Qwen
    try:
        raw_reply = ask_qwen(prompt, req.lang)
        reply_log = raw_reply[:200].replace('\n', ' ')
        logging.info(f"[QWEN] Raw reply: {reply_log}...")
    except Exception as e:
        logging.error(f"[❌ QWEN ERROR] {e}")
        raise HTTPException(status_code=502, detail=f"Error calling Qwen: {e}")

    # 5. Clean ตอบกลับสำหรับ TTS
    reply_clean = clean_text_for_tts(raw_reply)
  

    return {
        "answer": reply_clean,
        "context": context,
        "documents": docs
    }
