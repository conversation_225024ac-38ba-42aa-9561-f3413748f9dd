# app/api/delete_router.py
from fastapi import APIRouter, HTTPException
from app.models.schema import DeleteRequest, DeleteResponse, DeleteAllResponse, DeleteBySourceRequest , DeleteAllRequest
from app.services.delete_manager import delete_by_id, delete_all_by_class, delete_by_source

router = APIRouter(prefix="/vector", tags=["delete"])

@router.delete("", response_model=DeleteResponse)
async def delete_vector(req: DeleteRequest):
    try:
        delete_by_id(req.object_id, req.class_name)
        return DeleteResponse(status="deleted", object_id=req.object_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/all", response_model=DeleteAllResponse)
async def delete_all(req: DeleteAllRequest):
    try:
        count = delete_all_by_class(req.class_name, req.batch_size)
        return DeleteAllResponse(status="deleted_all", class_name=req.class_name, count=count)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/source", response_model=DeleteAllResponse)
async def delete_by_filename(req: DeleteBySourceRequest):
    try:
        count = delete_by_source(req.filename)
        return DeleteAllResponse(status="deleted_by_source", class_name="Document", count=count)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
