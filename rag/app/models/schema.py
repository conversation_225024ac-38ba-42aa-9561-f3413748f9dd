from pydantic import BaseModel, Field
from typing import List, Optional


class AskRequest(BaseModel):
    speak: str = Field(..., description="คำถามที่ผู้ใช้ป้อน")
    lang: str = Field(..., description="ภาษาที่ต้องการให้ Qwen ตอบ เช่น 'th', 'en'")


class Document(BaseModel):
    text: str
    source: str
    page: Optional[int] = None
    chunk: Optional[int] = None


class AskResponse(BaseModel):
    answer: str
    context: str
    documents: List[Document]


class IngestResponse(BaseModel):
    status: str
    count: int


class DeleteRequest(BaseModel):
    object_id: str
    class_name: str = Field(default="Document")


class DeleteResponse(BaseModel):
    status: str
    object_id: str

class DeleteAllRequest(BaseModel):
    class_name: str = "Document"
    batch_size: int = 100
    
class DeleteAllResponse(BaseModel):
    status: str
    class_name: str
    count: int

class DeleteBySourceRequest(BaseModel):
    filename: str

class FilenameRequest(BaseModel):
    filename: str

class IngestSinceRequest(BaseModel):
    iso_datetime: str