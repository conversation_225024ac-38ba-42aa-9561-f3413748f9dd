# app/models/weaviate_schema.py
DOCUMENT_CLASS_SCHEMA = {
    "class": "Document",
    "vectorIndexType": "hnsw",
    "properties": [
        {"name": "text", "dataType": ["text"]},
        {"name": "source", "dataType": ["text"]},
        {"name": "page", "dataType": ["int"]},
        {"name": "chunk", "dataType": ["int"]},
        {"name": "filetype", "dataType": ["text"]},
        {"name": "fullpath", "dataType": ["text"]},
        {"name": "ingested_at", "dataType": ["date"]},
        {"name": "section", "dataType": ["text"]},
        {"name": "subsection", "dataType": ["text"]}  # 👈 เพิ่มตรงนี้
    ]
}