หมวดหมู่,เนื้อหา
สรุปประวัติส่วนตัว,"วิศวกรซอฟต์แวร์ที่มีความหลงใหลในงานและมุ่งเน้นผลลัพธ์ ด้วยประสบการณ์มากกว่า 13 ปีในการพัฒนาแบบครบวงจร ทั้งด้าน Full-Stack, ออกแบบระบบ, ออกแบบฐานข้อมูล และโซลูชันที่ใช้ AI มีทักษะในการออกแบบระบบที่สามารถขยายได้และดูแลรักษาได้ง่าย ครอบคลุมตั้งแต่การวางแนวคิด การออกแบบฐานข้อมูล ไปจนถึงการนำไปใช้งานและการปรับแต่งประสิทธิภาพ มีผลงานที่พิสูจน์ได้ ได้แก่:
- การออกแบบระบบและสถาปัตยกรรม: ออกแบบระบบแบบ Microservices และ Monolithic โดยใช้ Node.js, ASP.NET Core และ FastAPI
- การออกแบบและบริหารจัดการฐานข้อมูล: ออกแบบฐานข้อมูลแบบ Relational และ NoSQL (MySQL, PostgreSQL, MongoDB) ที่เน้นความพร้อมใช้งานสูงและการตอบสนองที่รวดเร็ว
- การพัฒนาแบบ Full-Stack: สร้าง Frontend ที่ตอบสนองรวดเร็วด้วย SvelteKit, React และ Backend ที่แข็งแกร่งด้วย Express, .NET และ PHP
- DevOps และการนำระบบไปใช้งาน: ทำระบบ CI/CD อัตโนมัติด้วย Docker, Kubernetes, Nginx และ PM2 บน AWS, GCP และ DigitalOcean
- AI และ Computer Vision: พัฒนาระบบตรวจจับวัตถุ YOLOv11, แพลตฟอร์มสนทนา RAG ด้วยข้อความ/เสียง, ระบบฝังเวกเตอร์ และ LLM/TTS/ASR ที่โฮสต์เอง"
ทักษะทางเทคนิค,"ภาษาและเฟรมเวิร์ก: Node.js, ASP.NET MVC/Core, PHP, React, SvelteKit, React Native, Laravel, FastAPI, Java, VB.NET, YII
ฐานข้อมูล: MongoDB, MySQL, SQL Server, Oracle 11g, MariaDB, MS Access
DevOps และ Cloud: Docker, Nginx, PM2, AWS, Google Cloud, DigitalOcean, Heroku, Vercel, Firebase, Ubuntu/Windows Server
เทคโนโลยีเฉพาะทาง: YOLOv11, Darknet, RAG, LLM, Whisper, <PERSON> Bot, Blockchain, Smart Contract, RFID, Jasper Reports, WebSocket, AI/ML"
ผลงานเด่น,"ระบบตรวจจับสินค้า YOLOv11 (2023) 🛠 YOLOv11, Darknet, OpenCV, FastAPI, Docker
แพลตฟอร์มสนทนา AI ด้วยข้อความและเสียง (2023) 🛠 Weaviate/FAISS, Stella 400M, LangChain, Coqui TTS, faster-whisper, Qwen V3, Docker/Kubernetes
แอปสั่งอาหารสดแบบเรียลไทม์ด้วย SvelteKit (2023) 🛠 SvelteKit, Node.js, Socket.IO, MongoDB
ระบบตรวจสอบตู้แช่ Pepsi ด้วย AI (2023) 🛠 YOLOv11, Docker, AWS EC2"
การศึกษาและความเชี่ยวชาญ,"วท.ม. เทคโนโลยีสารสนเทศ มหาวิทยาลัยขอนแก่น (2554–2557)
วท.บ. เทคโนโลยีสารสนเทศและการสื่อสาร มหาวิทยาลัยมหาสารคาม (2551–2553)
ปวส. เทคโนโลยีสารสนเทศ วิทยาลัยเทคนิคขอนแก่น (2549–2551)

โครงการส่วนตัวและความสนใจ
- พัฒนา Smart Contract บน Blockchain
- สร้างระบบ LINE Bot แบบกำหนดเอง
- สำรวจการผสาน AI เข้ากับระบบ RAG, TTS และ LLM

ทักษะและความชำนาญเฉพาะด้าน
Frontend & UI: Svelte, SvelteKit, React, React Native, VueJS, HTML, CSS, Bootstrap, AJAX, WordPress, Elementor, LINE LIFF
Backend & Server: Node.js, Express, PHP, ASP.NET Core/MVC, FastAPI, Java, VB.NET, C, Laravel, YII, Socket.IO, PM2, Log4net
ฐานข้อมูล: MongoDB, MySQL, MariaDB, SQL Server, Oracle 11g, MS Access, Entity Framework
DevOps & Deployment: Docker, Nginx, Heroku, Vercel, DigitalOcean, AWS, Google Cloud, Ubuntu/Windows Server, Cloudflare
ระบบรายงานและการแสดงผล: JasperReports, Crystal Reports, Report Viewer, Google Analytics
เทคโนโลยีใหม่และ AI/ML: YOLO, Darknet, Machine Learning, LLMs, Whisper, การประมวลผลภาพ, การจำแนกภาพ, การตรวจจับวัตถุ
Blockchain: Solidity, Blockchain, Smart Contracts, Cryptocurrency
IoT และฮาร์ดแวร์: Arduino, Microcontrollers, RFID, IoT, Smart Farm, CLIPS
อื่น ๆ: LineBot, Web Scraping, SEO, Live Streaming, การถ่ายวิดีโอ, การเทรด, JSON, XML
ความรู้เชิงลึกและความเข้าใจ:
• การวิเคราะห์และออกแบบระบบ
• การจัดการและออกแบบฐานข้อมูล
• การเขียนโปรแกรมแบบ Full Stack
• RESTful API
• การจัดการเครือข่ายและเซิร์ฟเวอร์
• การทำเหมืองข้อมูล (Data Mining)
• การผสาน IoT กับ Smart Farm
• ระบบโลจิสติกส์และโซ่อุปทาน
• การประมวลผลบนคลาวด์
• การสื่อสารด้วย WebSocket
• การถ่ายทอดสดวิดีโอและภาพ
• การรวม AI เช่น LLMs, YOLO, Whisper
• การพัฒนา Blockchain และ Line Bot
• การจำแนกและตรวจจับวัตถุจากภาพ"
