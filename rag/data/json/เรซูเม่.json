[{"text": "สวัสดีครับผมชื่อ ทวีพงษ์ ทุมพัง ขณะนี้ผมเป็น ซอฟแวร์เอนจิเนียร์อยู่ที่บริษัทสกายเอไอ หน้าที่รับผิดชอบก็คือ พัฒนาระบบ วางโครงสร้างระบบ ซัพพอร์ทโปรแกรมเมอร์เวลาติดปัญหาอะไร ทำเรื่องที่เป็นไปได้ยากหรือเป็นไปไม่ได้ให้เป็นไปได้เพื่อทำงานให้เสร็จทันตามเวลาส่งมอบ ศึกษาการทำงาน business ขององค์กรเพื่อหาข้อบกพร่อง และปรับปรุงข้อบกพร่องหรือพัฒนากระบวนการของระบบของบริษัทให้ดีขึ้น งานที่ specialist เช่นหาสาเหตุต่างๆเหตุการณ์ต่างๆของตาม log server การใช้งาน config server docker nginx อื่นๆอีกมากมาย", "metadata": {"section": "แนะนำตัว"}}, {"text": "ตำแหน่งงานที่สมัคร Dev<PERSON>per", "metadata": {"section": "ตำแหน่งที่สมัคร"}}, {"text": "ตามตกลง", "metadata": {"section": "เงินเดือนที่ต้องการ"}}, {"text": "ขับเคลื่อนองค์กรด้วยพลังของเทคโนโลยี โดยมุ่งเน้นการพัฒนาระบบและผลิตภัณฑ์ที่ตอบโจทย์การใช้งานจริง สร้างมูลค่าทางธุรกิจ และช่วยเสริมศักยภาพในการแข่งขัน พร้อมยกระดับรายได้และความมั่นคงขององค์กรในระยะยาว", "metadata": {"section": "เป้าหมายในการทำงาน"}}, {"text": "วิศวกรซอฟต์แวร์ที่มีความหลงใหลในงานและมุ่งเน้นผลลัพธ์ ด้วยประสบการณ์มากกว่า 13 ปีในการพัฒนาแบบครบวงจร ทั้งด้าน Full-Stack, ออกแบบระบบ, ออกแบบฐานข้อมูล และโซลูชันที่ใช้ AI มีทักษะในการออกแบบระบบที่สามารถขยายได้และดูแลรักษาได้ง่าย ครอบคลุมตั้งแต่การวางแนวคิด การออกแบบฐานข้อมูล ไปจนถึงการนำไปใช้งานและการปรับแต่งประสิทธิภาพ มีผลงานที่พิสูจน์ได้ ได้แก่การออกแบบระบบและสถาปัตยกรรมออกแบบระบบแบบ Microservices และ Monolithic โดยใช้ Node.js, ASP.NET Core และ FastAPI การออกแบบและบริหารจัดการฐานข้อมูล: ออกแบบฐานข้อมูลแบบ Relational และ NoSQL (MySQL, PostgreSQL, MongoDB) ที่เน้นความพร้อมใช้งานสูงและการตอบสนองที่รวดเร็ว การพัฒนาแบบ Full-Stack: สร้าง Frontend ที่ตอบสนองรวดเร็วด้วย SvelteKit, React และ Backend ที่แข็งแกร่งด้วย Express, .NET และ PHP DevOps และการนำระบบไปใช้งาน: ทำระบบ CI/CD อัตโนมัติด้วย Docker, Kubernetes, Nginx และ PM2 บน AWS, GCP และ DigitalOcean  AI และ Computer Vision: พัฒนาระบบตรวจจับวัตถุ YOLOv11 และ แพลตฟอร์มสนทนา RAG ด้วยข้อความเสียง, ระบบฝังเวกเตอร์ และ LLM TTS และ ASR ที่โฮสต์เอง", "metadata": {"section": "ประวัติส่วนตัว"}}, {"text": "ปริญญาโท สาขาเทคโนโลยีสารสนเทศมหาวิทยาลัยขอนแก่น ปริญญาตรีสาขาเทคโนโลยีสารสนเทศและการสื่อสารมหาวิทยาลัยมหาสารคาม ประกาศนียบัตรวิชาชีพชั้นสูงสาขาเทคโนโลยีสารสนเทศวิทยาลัยเทคนิคขอนแก่น", "metadata": {"section": "ประวัติการศึกษา"}}, {"text": "ระบบตรวจจับสินค้า YOLOv11 (2023) 🛠 YOLOv11, Darknet, OpenCV, FastAPI, Docker\nแพลตฟอร์มสนทนา AI ด้วยข้อความและเสียง (2023) 🛠 Weaviate/FAISS, Stella 400M, Lang<PERSON><PERSON><PERSON>, <PERSON><PERSON>, faster-whisper, Qwen V3, Docker/Kubernetes\nแอปสั่งอาหารสดแบบเรียลไทม์ด้วย SvelteKit (2023) 🛠 SvelteKit, Node.js, Socket.IO, MongoDB\nระบบตรวจสอบตู้แช่ Pepsi ด้วย AI (2023) 🛠 YOLOv11, <PERSON>er, AWS EC2", "metadata": {"section": "ผลงาน"}}, {"text": "javascript, typescript, php, python, c#, java, vb.net, c, c++, XML, CSS, HTML, JSON, YAML, SQL, NoSQL, solidity", "metadata": {"section": "ทักษะ", "subsection": "ภาษาที่ใช้พัฒนาระบบ"}}, {"text": "sveltekit, react, react native, laravel PHP, ASP.NET MVC, C#.NET, VB.NET, fastapi, yii", "metadata": {"section": "ทักษะ", "subsection": "ฟร้อนเอ็น"}}, {"text": "node.js, express, nestjs, .net core, java, VB.NET, C#.NET", "metadata": {"section": "ทักษะ", "subsection": "แบ็คเอ็น"}}, {"text": "Docker, Nginx, PM2, AWS, Google Cloud, DigitalOcean, Heroku, Vercel, Firebase, Ubuntu/Windows Server", "metadata": {"section": "ทักษะ", "subsection": "ออโต้เมชั่น"}}, {"text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "metadata": {"section": "ทักษะ", "subsection": "เว็บ เซอร์เวอร์"}}, {"text": "Vector Database, MongoDB, Redis, MySQL, SQL Server, Oracle 11g, MariaDB, MS Access", "metadata": {"section": "", "subsection": "ฐานข้อมูล"}}, {"text": "Yolo, Darknet, RAG , Vector Database, LLM, AI/ML, Object Detech, Image Classification, LLM, Whisper, Line Bot, Blockchain, Smart Contract, RFID, Jasper Reports, WebSocket", "metadata": {"section": "", "subsection": "สเปเชี่ยลลิส"}}]