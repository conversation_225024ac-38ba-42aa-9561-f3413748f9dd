[{"text": "Passionate and results-driven Software Engineer with 13+ years of comprehensive expertise across full-stack development, system architecture, database design, and AI-powered solutions. Skilled in designing scalable, maintainable systems—from conceptualization and database modeling to deployment and performance optimization. Proven track record includes:\n- System Design & Architecture: Architected microservices and monolithic systems using Node.js, ASP.NET Core, and FastAPI.\n- Database Design & Management: Designed relational and NoSQL schemas (MySQL, PostgreSQL, MongoDB) optimized for high availability and low latency.\n- Full-Stack Development: Delivered responsive frontends with SvelteKit, React, and robust backends with Express, .NET, and PHP frameworks.\n- DevOps & Deployment: Automated CI/CD pipelines with Docker, Kubernetes, Nginx, and PM2 on AWS, GCP, and DigitalOcean.\n- AI & Computer Vision: Developed YOLOv11 detection systems, RAG chat/voice platforms, embedding pipelines, and self-hosted LLM/TTS/ASR solutions.", "metadata": {"section": "Profile Summary"}}, {"text": "Languages & Frameworks: Node.js, ASP.NET MVC/Core, PHP, React, SvelteKit, React Native, Laravel, FastAPI, Java, VB.NET, YII\nDatabases: MongoDB, MySQL, SQL Server, Oracle 11g, MariaDB, MS Access\nDevOps & Cloud: Docker, Nginx, PM2, AWS, Google Cloud, DigitalOcean, Heroku, Vercel, Firebase, Ubuntu/Windows Server\nSpecialized Technologies: YOLOv11, Darknet, RAG, LLM, Whisper, Line Bot, Blockchain, Smart Contract, RFID, Jasper Reports, WebSocket, AI/ML", "metadata": {"section": "Technical Skills"}}, {"text": "YOLOv11 Product Detection (2023) 🛠 YOLOv11, Darknet, OpenCV, FastAPI, Docker\nAI Chat & Voice Platform (2023) 🛠 Weaviate/FAISS, Stella 400M, LangChain, Coqui TTS, faster-whisper, Qwen V3, Docker/Kubernetes\nSvelteKit Live Ordering App (2023) 🛠 SvelteKit, Node.js, Socket.IO, MongoDB\nPepsi Cooler AI Monitoring (2023) 🛠 YOLOv11, Docker, AWS EC2", "metadata": {"section": "Key Projects"}}, {"text": "<PERSON><PERSON>Sc. in Information Technology, Khon <PERSON> University (2011–2014)\nB.Sc. in IT and Communication, Mahasarakham University (2008–2010)\nHigh Vocational Certificate in IT, Khon <PERSON> Technical College (2006–2008)\nPersonal Projects & Interests\nDeveloping Blockchain Smart Contracts\nBuilding Custom LINE Bot Systems\nExploring AI Integrations with RAG, TTS, and LLM\nSkills & Expertise\nFrontend & UI: Svelte, SvelteKit, React, React Native, VueJS, HTML, CSS, Bootstrap, AJAX, WordPress, Elementor, LINE LIFF\nBackend & Server: Node.js, Express, PHP, ASP.NET Core/MVC, FastAPI, Java, VB.NET, C, Laravel, YII, Socket.IO, PM2, Log4net\nDatabases: MongoDB, MySQL, MariaDB, SQL Server, Oracle 11g, MS Access, Entity Framework\nDevOps & Deployment: Docker, Nginx, Heroku, Vercel, DigitalOcean, AWS, Google Cloud, Ubuntu/Windows Server, Cloudflare\nReports & Visualization: JasperReports, Crystal Reports, Report Viewer, Google Analytics\nEmerging Tech & AI/ML: YOLO, Darknet, Machine Learning, LLMs, Whisper, Image Processing, Classification, Detection\nBlockchain: Solidity, Blockchain, Smart Contracts, Cryptocurrency\nIoT & Hardware: Arduino, Microcontrollers, RFID, IoT, Smart Farm, CLIPS\nOthers: LineBot, Web Scraping, SEO, Live Streaming, Videography, Trading, JSON, XML\nDomain Knowledge & Understanding\n• System Analysis & Design\n• Database Management & Design\n• Programming (Full Stack)\n• RESTful API\n• Network & Server Management\n• Data Mining\n• Smart Farm & IoT Integration\n• Logistic & Supply Chain\n• Cloud Computing\n• WebSocket Communication\n• Live Video & Image Streaming\n• AI Integration: LLMs, YOLO, Whisper\n• Blockchain & Line Bot Development\n• Image Classification and Detection", "metadata": {"section": "Education"}}]