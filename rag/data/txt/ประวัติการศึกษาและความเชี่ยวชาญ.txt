ประวัติการศึกษา:
วท.ม. เทคโนโลยีสารสนเทศ มหาวิทยาลัยขอนแก่น (2554–2557)
วท.บ. เทคโนโลยีสารสนเทศและการสื่อสาร มหาวิทยาลัยมหาสารคาม (2551–2553)
ปวส. เทคโนโลยีสารสนเทศ วิทยาลัยเทคนิคขอนแก่น (2549–2551)

โครงการส่วนตัวและความสนใจ: 
พัฒนา Smart Contract บน Blockchain
สร้างระบบ LINE Bot แบบกำหนดเอง
สำรวจการผสาน AI เข้ากับระบบ RAG, TTS และ LLM

สกิล&ทักษะความสามารถ:
Frontend & UI: Svelte, SvelteKit, React, React Native, VueJS, HTML, CSS, Bootstrap, AJAX, WordPress, Elementor, LINE LIFF
Backend & Server: Node.js, Express, PHP, ASP.NET Core/MVC, FastAPI, Java, VB.NET, C, Laravel, YII, Socket.IO, PM2, Log4net
ฐานข้อมูล: MongoDB, MySQL, MariaDB, SQL Server, Oracle 11g, MS Access, Entity Framework
DevOps & Deployment: Docker, Nginx, Heroku, Vercel, DigitalOcean, AWS, Google Cloud, Ubuntu/Windows Server, Cloudflare
ระบบรายงานและการแสดงผล: JasperReports, Crystal Reports, Report Viewer, Google Analytics
เทคโนโลยีใหม่และ AI/ML: YOLO, Darknet, Machine Learning, LLMs, Whisper, การประมวลผลภาพ, การจำแนกภาพ, การตรวจจับวัตถุ
Blockchain: Solidity, Blockchain, Smart Contracts, Cryptocurrency
IoT และฮาร์ดแวร์: Arduino, Microcontrollers, RFID, IoT, Smart Farm, CLIPS
อื่น ๆ: LineBot, Web Scraping, SEO, Live Streaming, การถ่ายวิดีโอ, การเทรด, JSON, XML

ความรู้เชิงลึกและความเข้าใจ:
การวิเคราะห์และออกแบบระบบ การจัดการและออกแบบฐานข้อมูล การเขียนโปรแกรมแบบ Full Stack RESTful API การจัดการเครือข่ายและเซิร์ฟเวอร์ การทำเหมืองข้อมูล (Data Mining) การผสาน IoT กับ Smart Farm ระบบโลจิสติกส์และโซ่อุปทาน การประมวลผลบนคลาวด์ การสื่อสารด้วย WebSocket การถ่ายทอดสดวิดีโอและภาพ การรวม AI เช่น LLMs, YOLO, Whisper การพัฒนา Blockchain และ Line Bot การจำแนกและตรวจจับวัตถุจากภาพ