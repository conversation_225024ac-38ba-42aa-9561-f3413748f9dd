<script lang="ts">
  import { Icon, Microphone } from 'svelte-hero-icons';
  import { onMount } from 'svelte';

  let status: 'idle' | 'recording' | 'processing' = 'idle';
  let recognition: SpeechRecognition | null = null;
  let transcript = '';
  let errorMessage = '';
  let tick: HTMLAudioElement;

  const RAG_URL = 'https://rag.aivoices.work/ask';
  const TTS_URL = 'https://tts.aivoices.work/tts';

  onMount(() => {
    tick = new Audio('/tick.mp3');
  });

  function detectLanguageFromText(text: string): string {
    if (/[฀-๿]/.test(text)) return 'th';
    if (/[一-鿿]/.test(text)) return 'zh';
    if (/[぀-ヿ]/.test(text)) return 'ja';
    if (/[가-힯]/.test(text)) return 'ko';
    if (/^[a-zA-Z0-9\s.,?!'"-]+$/.test(text)) return 'en';
    return 'en';
  }

  async function startListening() {
    if (!('webkitSpeechRecognition' in window)) {
      errorMessage = 'เบราว์เซอร์ไม่รองรับการฟังเสียง';
      return;
    }

    try {
      await tick.play();
    } catch (e) {
      console.warn('ไม่สามารถเล่นเสียง tick ได้:', e);
    }

    const sr = new webkitSpeechRecognition();
    recognition = sr;
    sr.lang = 'th-TH';
    sr.continuous = false;
    sr.interimResults = false;

    status = 'recording';

    sr.onresult = async (e) => {
      const text = e.results[0][0].transcript;
      transcript = text;
      status = 'processing';

      const lang = detectLanguageFromText(text);
      await sendToBackend(text, lang);
    };

    sr.onerror = (e) => {
      errorMessage = `เกิดข้อผิดพลาด: ${e.error}`;
      status = 'idle';
    };

    sr.onend = () => {
      if (status === 'recording') {
        status = 'idle';
      }
    };

    sr.start();
  }

  async function sendToBackend(text: string, lang: string) {
    try {
      const res = await fetch(RAG_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ speak: text, lang })
      });
      const result = await res.json();
      await playTTS(result.answer, lang);
    } catch (err) {
      errorMessage = 'ปัญหาในการส่งไป /ask: ' + err;
      status = 'idle';
    }
  }

  async function playTTS(text: string, lang: string) {
    try {
      const res = await fetch(TTS_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text, lang, slow: false })
      });

      const blob = await res.blob();
      const url = URL.createObjectURL(blob);
      const audio = new Audio(url);

      audio.onended = () => {
        URL.revokeObjectURL(url);
        status = 'idle';
      };
      audio.onerror = () => {
        errorMessage = 'เกิดข้อผิดพลาดขณะเล่นเสียง';
        status = 'idle';
        URL.revokeObjectURL(url);
      };

      audio.play();
    } catch (err) {
      errorMessage = 'เกิดข้อผิดพลาด TTS: ' + err;
      status = 'idle';
    }
  }
</script>

<main class="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center">
  <header class="text-center mb-12">
    <h1 class="text-5xl font-bold text-blue-400">AI Contact Center</h1>
    <p class="text-lg text-gray-300 mt-2">ขับเคลื่อนด้วย AI เพื่อสร้างอนาคตที่ดีกว่า</p>
  </header>

  <div class="relative w-24 h-24 flex items-center justify-center">
    <div
      class={`p-6 rounded-full focus:outline-none focus:ring-4 transition-colors duration-200 z-20
        ${status === 'idle' ? 'bg-blue-500 hover:bg-blue-600 focus:ring-blue-400' :
          status === 'recording' ? 'bg-red-500 hover:bg-red-600 focus:ring-red-400' :
          status === 'processing' ? 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-400' : ''}`}
      on:click={startListening}
      role="button"
      tabindex="0"
    >
      <Icon src={Microphone} class="w-16 h-16 text-white" />
    </div>
  </div>

  {#if errorMessage}
    <p class="mt-4 text-red-400">⚠️ {errorMessage}</p>
  {/if}

  <footer class="mt-12 text-gray-500 text-sm text-center">
    © 2025 AI Contact Center
  </footer>
</main>

<style>
  * {
    touch-action: manipulation;
  }
</style>
