const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["favicon.png","tick.mp3"]),
	mimeTypes: {".png":"image/png",".mp3":"audio/mpeg"},
	_: {
		client: {start:"_app/immutable/entry/start.CQzukjwC.js",app:"_app/immutable/entry/app.w_9DqooR.js",imports:["_app/immutable/entry/start.CQzukjwC.js","_app/immutable/chunks/DgEu87TS.js","_app/immutable/chunks/DETHqu2A.js","_app/immutable/chunks/Bh1x9_Ty.js","_app/immutable/entry/app.w_9DqooR.js","_app/immutable/chunks/DETHqu2A.js","_app/immutable/chunks/BtLhCfhZ.js","_app/immutable/chunks/DmJh76ia.js","_app/immutable/chunks/Bh1x9_Ty.js","_app/immutable/chunks/CrsDrFAH.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./chunks/0-BlfO0SKg.js')),
			__memo(() => import('./chunks/1-BwlJcuQF.js')),
			__memo(() => import('./chunks/2-dZWNv1aq.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();

const prerendered = new Set([]);

const base = "";

export { base, manifest, prerendered };
//# sourceMappingURL=manifest.js.map
