{"version": 3, "file": "_page.svelte-Ci6vdsQp.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/_page.svelte.js"], "sourcesContent": ["import { u as sanitize_props, v as rest_props, w as fallback, x as ensure_array_like, y as spread_attributes, z as bind_props, m as pop, p as push, A as attr_class } from \"../../chunks/index.js\";\nconst Microphone = { \"default\": { \"a\": { \"fill\": \"none\", \"viewBox\": \"0 0 24 24\", \"stroke-width\": \"1.5\", \"stroke\": \"currentColor\", \"aria-hidden\": \"true\" }, \"path\": [{ \"stroke-linecap\": \"round\", \"stroke-linejoin\": \"round\", \"d\": \"M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z\" }] }, \"micro\": { \"a\": { \"viewBox\": \"0 0 16 16\", \"fill\": \"currentColor\", \"aria-hidden\": \"true\" }, \"path\": [{ \"d\": \"M8 1a2 2 0 0 0-2 2v4a2 2 0 1 0 4 0V3a2 2 0 0 0-2-2Z\" }, { \"d\": \"M4.5 7A.75.75 0 0 0 3 7a5.001 5.001 0 0 0 4.25 4.944V13.5h-1.5a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-1.5v-1.556A5.001 5.001 0 0 0 13 7a.75.75 0 0 0-1.5 0 3.5 3.5 0 1 1-7 0Z\" }] }, \"mini\": { \"a\": { \"viewBox\": \"0 0 20 20\", \"fill\": \"currentColor\", \"aria-hidden\": \"true\" }, \"path\": [{ \"d\": \"M7 4a3 3 0 0 1 6 0v6a3 3 0 1 1-6 0V4Z\" }, { \"d\": \"M5.5 9.643a.75.75 0 0 0-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-1.5v-1.546A6.001 6.001 0 0 0 16 10v-.357a.75.75 0 0 0-1.5 0V10a4.5 4.5 0 0 1-9 0v-.357Z\" }] }, \"solid\": { \"a\": { \"viewBox\": \"0 0 24 24\", \"fill\": \"currentColor\", \"aria-hidden\": \"true\" }, \"path\": [{ \"d\": \"M8.25 4.5a3.75 3.75 0 1 1 7.5 0v8.25a3.75 3.75 0 1 1-7.5 0V4.5Z\" }, { \"d\": \"M6 10.5a.75.75 0 0 1 .75.75v1.5a5.25 5.25 0 1 0 10.5 0v-1.5a.75.75 0 0 1 1.5 0v1.5a6.751 6.751 0 0 1-6 6.709v2.291h3a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3v-2.291a6.751 6.751 0 0 1-6-6.709v-1.5A.75.75 0 0 1 6 10.5Z\" }] } };\nfunction Icon($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const $$restProps = rest_props($$sanitized_props, [\"src\", \"size\", \"solid\", \"mini\", \"micro\"]);\n  push();\n  let icon;\n  let src = $$props[\"src\"];\n  let size = fallback($$props[\"size\"], \"100%\");\n  let solid = fallback($$props[\"solid\"], false);\n  let mini = fallback($$props[\"mini\"], false);\n  let micro = fallback($$props[\"micro\"], false);\n  if (size !== \"100%\") {\n    if (size.slice(-1) != \"x\" && size.slice(-1) != \"m\" && size.slice(-1) != \"%\") {\n      try {\n        size = parseInt(size) + \"px\";\n      } catch (error) {\n        size = \"100%\";\n      }\n    }\n  }\n  icon = src?.[solid ? \"solid\" : mini ? \"mini\" : micro ? \"micro\" : \"default\"];\n  const each_array = ensure_array_like(icon?.path ?? []);\n  $$payload.out += `<svg${spread_attributes(\n    {\n      ...icon?.a,\n      xmlns: \"http://www.w3.org/2000/svg\",\n      width: size,\n      height: size,\n      \"aria-hidden\": \"true\",\n      ...$$restProps\n    },\n    null,\n    void 0,\n    void 0,\n    3\n  )}><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let a = each_array[$$index];\n    $$payload.out += `<path${spread_attributes({ ...a }, null, void 0, void 0, 3)}></path>`;\n  }\n  $$payload.out += `<!--]--></svg>`;\n  bind_props($$props, { src, size, solid, mini, micro });\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  $$payload.out += `<main class=\"min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center svelte-32cf93\"><header class=\"text-center mb-12 svelte-32cf93\"><h1 class=\"text-5xl font-bold text-blue-400 svelte-32cf93\">AI Contact Center</h1> <p class=\"text-lg text-gray-300 mt-2 svelte-32cf93\">ขับเคลื่อนด้วย AI เพื่อสร้างอนาคตที่ดีกว่า</p></header> <div class=\"relative w-24 h-24 flex items-center justify-center svelte-32cf93\"><div${attr_class(\n    `p-6 rounded-full focus:outline-none focus:ring-4 transition-colors duration-200 z-20\n        ${\"bg-blue-500 hover:bg-blue-600 focus:ring-blue-400\"}`,\n    \"svelte-32cf93\"\n  )} role=\"button\" tabindex=\"0\">`;\n  Icon($$payload, {\n    src: Microphone,\n    class: \"w-16 h-16 text-white\"\n  });\n  $$payload.out += `<!----></div></div> `;\n  {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <footer class=\"mt-12 text-gray-500 text-sm text-center svelte-32cf93\">© 2025 AI Contact Center</footer></main>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;AACA,MAAM,UAAU,GAAG,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,gBAAgB,EAAE,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,GAAG,EAAE,4IAA4I,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,qDAAqD,EAAE,EAAE,EAAE,GAAG,EAAE,mLAAmL,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,uCAAuC,EAAE,EAAE,EAAE,GAAG,EAAE,4MAA4M,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,iEAAiE,EAAE,EAAE,EAAE,GAAG,EAAE,8NAA8N,EAAE,CAAC,EAAE,EAAE;AACr+C,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC9F,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI;AACV,EAAE,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC;AAC1B,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;AAC9C,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC;AAC/C,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC;AAC7C,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC;AAC/C,EAAE,IAAI,IAAI,KAAK,MAAM,EAAE;AACvB,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE;AACjF,MAAM,IAAI;AACV,QAAQ,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI;AACpC,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,IAAI,GAAG,MAAM;AACrB;AACA;AACA;AACA,EAAE,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,OAAO,GAAG,IAAI,GAAG,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,SAAS,CAAC;AAC7E,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC;AACxD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,GAAG,IAAI,EAAE,CAAC;AAChB,MAAM,KAAK,EAAE,4BAA4B;AACzC,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,aAAa,EAAE,MAAM;AAC3B,MAAM,GAAG;AACT,KAAK;AACL,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,CAAC,SAAS,CAAC;AACd,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;AAC3F;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AACxD,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,2aAA2a,EAAE,UAAU;AAC3c,IAAI,CAAC;AACL,QAAQ,EAAE,mDAAmD,CAAC,CAAC;AAC/D,IAAI;AACJ,GAAG,CAAC,4BAA4B,CAAC;AACjC,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,GAAG,EAAE,UAAU;AACnB,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzC,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uHAAuH,CAAC;AAC5I,EAAE,GAAG,EAAE;AACP;;;;"}