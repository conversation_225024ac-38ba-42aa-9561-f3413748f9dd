import { p as push, u as attr_class, m as pop, v as sanitize_props, w as rest_props, x as fallback, y as ensure_array_like, z as spread_attributes, A as bind_props } from './index-alSqolgh.js';

const Microphone = { "default": { "a": { "fill": "none", "viewBox": "0 0 24 24", "stroke-width": "1.5", "stroke": "currentColor", "aria-hidden": "true" }, "path": [{ "stroke-linecap": "round", "stroke-linejoin": "round", "d": "M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z" }] }, "micro": { "a": { "viewBox": "0 0 16 16", "fill": "currentColor", "aria-hidden": "true" }, "path": [{ "d": "M8 1a2 2 0 0 0-2 2v4a2 2 0 1 0 4 0V3a2 2 0 0 0-2-2Z" }, { "d": "M4.5 7A.75.75 0 0 0 3 7a5.001 5.001 0 0 0 4.25 4.944V13.5h-1.5a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-1.5v-1.556A5.001 5.001 0 0 0 13 7a.75.75 0 0 0-1.5 0 3.5 3.5 0 1 1-7 0Z" }] }, "mini": { "a": { "viewBox": "0 0 20 20", "fill": "currentColor", "aria-hidden": "true" }, "path": [{ "d": "M7 4a3 3 0 0 1 6 0v6a3 3 0 1 1-6 0V4Z" }, { "d": "M5.5 9.643a.75.75 0 0 0-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-1.5v-1.546A6.001 6.001 0 0 0 16 10v-.357a.75.75 0 0 0-1.5 0V10a4.5 4.5 0 0 1-9 0v-.357Z" }] }, "solid": { "a": { "viewBox": "0 0 24 24", "fill": "currentColor", "aria-hidden": "true" }, "path": [{ "d": "M8.25 4.5a3.75 3.75 0 1 1 7.5 0v8.25a3.75 3.75 0 1 1-7.5 0V4.5Z" }, { "d": "M6 10.5a.75.75 0 0 1 .75.75v1.5a5.25 5.25 0 1 0 10.5 0v-1.5a.75.75 0 0 1 1.5 0v1.5a6.751 6.751 0 0 1-6 6.709v2.291h3a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3v-2.291a6.751 6.751 0 0 1-6-6.709v-1.5A.75.75 0 0 1 6 10.5Z" }] } };
function Icon($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, ["src", "size", "solid", "mini", "micro"]);
  push();
  let icon;
  let src = $$props["src"];
  let size = fallback($$props["size"], "100%");
  let solid = fallback($$props["solid"], false);
  let mini = fallback($$props["mini"], false);
  let micro = fallback($$props["micro"], false);
  if (size !== "100%") {
    if (size.slice(-1) != "x" && size.slice(-1) != "m" && size.slice(-1) != "%") {
      try {
        size = parseInt(size) + "px";
      } catch (error) {
        size = "100%";
      }
    }
  }
  icon = src?.[solid ? "solid" : mini ? "mini" : micro ? "micro" : "default"];
  const each_array = ensure_array_like(icon?.path ?? []);
  $$payload.out += `<svg${spread_attributes(
    {
      ...icon?.a,
      xmlns: "http://www.w3.org/2000/svg",
      width: size,
      height: size,
      "aria-hidden": "true",
      ...$$restProps
    },
    null,
    void 0,
    void 0,
    3
  )}><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let a = each_array[$$index];
    $$payload.out += `<path${spread_attributes({ ...a }, null, void 0, void 0, 3)}></path>`;
  }
  $$payload.out += `<!--]--></svg>`;
  bind_props($$props, { src, size, solid, mini, micro });
  pop();
}
function _page($$payload, $$props) {
  push();
  $$payload.out += `<main class="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center svelte-32cf93"><header class="text-center mb-12 svelte-32cf93"><h1 class="text-5xl font-bold text-blue-400 svelte-32cf93">AI Contact Center</h1> <p class="text-lg text-gray-300 mt-2 svelte-32cf93">ขับเคลื่อนด้วย AI เพื่อสร้างอนาคตที่ดีกว่า</p></header> <div class="relative w-24 h-24 flex items-center justify-center svelte-32cf93"><div${attr_class(
    `p-6 rounded-full focus:outline-none focus:ring-4 transition-colors duration-200 z-20
        ${"bg-blue-500 hover:bg-blue-600 focus:ring-blue-400"}`,
    "svelte-32cf93"
  )} role="button" tabindex="0">`;
  Icon($$payload, {
    src: Microphone,
    class: "w-16 h-16 text-white"
  });
  $$payload.out += `<!----></div></div> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <footer class="mt-12 text-gray-500 text-sm text-center svelte-32cf93">© 2025 AI Contact Center</footer></main>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Ci6vdsQp.js.map
