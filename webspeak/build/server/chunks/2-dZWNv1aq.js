const index = 2;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-Ci6vdsQp.js')).default;
const imports = ["_app/immutable/nodes/2.DhESM0d9.js","_app/immutable/chunks/DmJh76ia.js","_app/immutable/chunks/DETHqu2A.js","_app/immutable/chunks/mPnEdIO8.js","_app/immutable/chunks/Bh1x9_Ty.js","_app/immutable/chunks/BtLhCfhZ.js","_app/immutable/chunks/CrsDrFAH.js"];
const stylesheets = ["_app/immutable/assets/2.2cGi1pg3.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=2-dZWNv1aq.js.map
