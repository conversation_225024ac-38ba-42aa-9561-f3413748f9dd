{"version": 3, "file": "error.svelte-COYki1j6.js", "sources": ["../../../.svelte-kit/adapter-node/entries/fallbacks/error.svelte.js"], "sourcesContent": ["import { n as noop, q as getContext, t as escape_html, m as pop, p as push } from \"../../chunks/index.js\";\nimport \"clsx\";\nimport { w as writable } from \"../../chunks/exports.js\";\nconst SNAPSHOT_KEY = \"sveltekit:snapshot\";\nconst SCROLL_KEY = \"sveltekit:scroll\";\nfunction create_updated_store() {\n  const { set, subscribe } = writable(false);\n  {\n    return {\n      subscribe,\n      // eslint-disable-next-line @typescript-eslint/require-await\n      check: async () => false\n    };\n  }\n}\nconst is_legacy = noop.toString().includes(\"$$\") || /function \\w+\\(\\) \\{\\}/.test(noop.toString());\nif (is_legacy) {\n  ({\n    data: {},\n    form: null,\n    error: null,\n    params: {},\n    route: { id: null },\n    state: {},\n    status: -1,\n    url: new URL(\"https://example.com\")\n  });\n}\nfunction get(key, parse = JSON.parse) {\n  try {\n    return parse(sessionStorage[key]);\n  } catch {\n  }\n}\nget(SCROLL_KEY) ?? {};\nget(SNAPSHOT_KEY) ?? {};\nconst stores = {\n  updated: /* @__PURE__ */ create_updated_store()\n};\n({\n  check: stores.updated.check\n});\nfunction context() {\n  return getContext(\"__request__\");\n}\nconst page$1 = {\n  get error() {\n    return context().page.error;\n  },\n  get status() {\n    return context().page.status;\n  }\n};\nconst page = page$1;\nfunction Error$1($$payload, $$props) {\n  push();\n  $$payload.out += `<h1>${escape_html(page.status)}</h1> <p>${escape_html(page.error?.message)}</p>`;\n  pop();\n}\nexport {\n  Error$1 as default\n};\n"], "names": [], "mappings": ";;;AAGA,MAAM,YAAY,GAAG,oBAAoB;AACzC,MAAM,UAAU,GAAG,kBAAkB;AACrC,SAAS,oBAAoB,GAAG;AAChC,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC5C,EAAE;AACF,IAAI,OAAO;AACX,MAAM,SAAS;AACf;AACA,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL;AACA;AACA,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjG,IAAI,SAAS,EAAE;AACf,EAAE,CAAC;AACH,IAOI,GAAG,EAAE,IAAI,GAAG,CAAC,qBAAqB;AACtC,GAAG;AACH;AACA,SAAS,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;AACtC,EAAE,IAAI;AACN,IAAI,OAAO,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACrC,GAAG,CAAC,MAAM;AACV;AACA;AACA,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;AACrB,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;AACvB,MAAM,MAAM,GAAG;AACf,EAAE,OAAO,kBAAkB,oBAAoB;AAC/C,CAAC;AACD,CAAC;AACD,EAAE,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AACD,SAAS,OAAO,GAAG;AACnB,EAAE,OAAO,UAAU,CAAC,aAAa,CAAC;AAClC;AACA,MAAM,MAAM,GAAG;AACf,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK;AAC/B,GAAG;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM;AAChC;AACA,CAAC;AACD,MAAM,IAAI,GAAG,MAAM;AACnB,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC;AACpG,EAAE,GAAG,EAAE;AACP;;;;"}