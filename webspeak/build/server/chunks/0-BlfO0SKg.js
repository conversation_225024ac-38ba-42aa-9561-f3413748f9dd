const index = 0;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-BNXa52lF.js')).default;
const imports = ["_app/immutable/nodes/0.B0cZGs6j.js","_app/immutable/chunks/DmJh76ia.js","_app/immutable/chunks/DETHqu2A.js"];
const stylesheets = ["_app/immutable/assets/0.Dx8IRVaV.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=0-BlfO0SKg.js.map
