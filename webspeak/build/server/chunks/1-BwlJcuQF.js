const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./error.svelte-COYki1j6.js')).default;
const imports = ["_app/immutable/nodes/1.DmM0LVPY.js","_app/immutable/chunks/DmJh76ia.js","_app/immutable/chunks/DETHqu2A.js","_app/immutable/chunks/mPnEdIO8.js","_app/immutable/chunks/BtLhCfhZ.js","_app/immutable/chunks/DgEu87TS.js","_app/immutable/chunks/Bh1x9_Ty.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-BwlJcuQF.js.map
