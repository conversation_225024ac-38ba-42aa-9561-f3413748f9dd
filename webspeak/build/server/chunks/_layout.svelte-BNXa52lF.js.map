{"version": 3, "file": "_layout.svelte-BNXa52lF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/_layout.svelte.js"], "sourcesContent": ["import \"clsx\";\nfunction _layout($$payload, $$props) {\n  let { children } = $$props;\n  children($$payload);\n  $$payload.out += `<!---->`;\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": "AACA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC5B,EAAE,QAAQ,CAAC,SAAS,CAAC;AACrB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B;;;;"}