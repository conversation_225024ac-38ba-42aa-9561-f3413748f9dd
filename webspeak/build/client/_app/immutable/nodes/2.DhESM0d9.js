import{d as ae,a as z,f as ie}from"../chunks/DmJh76ia.js";import{i as se}from"../chunks/mPnEdIO8.js";import{o as we}from"../chunks/Bh1x9_Ty.js";import{a3 as Te,ag as ye,i as ne,C as P,m as k,a7 as Ee,y as Se,g as C,M as ke,z as Ce,A as Me,B as G,D as V,o as I,aa as Le,F as fe,j as q,G as Ie,aq as Z,ae as oe,a1 as F,ar as Ne,Y as B,I as J,a5 as Re,as as Oe,at as me,ad as Ve,au as Ue,k as le,av as $e,aw as ze,a8 as Be,ak as je,ax as He,ay as Pe,az as Ze,aA as De,aB as Ye,q as ce,aC as qe,aD as Ge,t as j,s as ue,Q as E,e as U,w as m,x as K,v as $,aE as Fe}from"../chunks/DETHqu2A.js";import{i as Je,c as Ke,d as Qe,n as Xe,a as We,e as xe,s as er}from"../chunks/BtLhCfhZ.js";import{l as Q,p as N,i as rr}from"../chunks/CrsDrFAH.js";function tr(e,t){if(t){const r=document.body;e.autofocus=!0,Te(()=>{document.activeElement===r&&e.focus()})}}function ar(e,t){return t}function ir(e,t,r,a){for(var i=[],f=t.length,o=0;o<f;o++)me(t[o].e,i,!0);var d=f>0&&i.length===0&&r!==null;if(d){var _=r.parentNode;Ve(_),_.append(r),a.clear(),M(e,t[0].prev,t[f-1].next)}Ue(i,()=>{for(var u=0;u<f;u++){var l=t[u];d||(a.delete(l.k),M(e,l.prev,l.next)),le(l.e,!d)}})}function sr(e,t,r,a,i,f=null){var o=e,d={flags:t,items:new Map,first:null};{var _=e;o=k?P(Ee(_)):_.appendChild(ye())}k&&Se();var u=null,l=!1,h=ke(()=>{var s=r();return Re(s)?s:s==null?[]:oe(s)});ne(()=>{var s=C(h),b=s.length;if(l&&b===0)return;l=b===0;let g=!1;if(k){var T=Ce(o)===Me;T!==(b===0)&&(o=G(),P(o),V(!1),g=!0)}if(k){for(var y=null,n,p=0;p<b;p++){if(I.nodeType===8&&I.data===Le){o=I,g=!0,V(!1);break}var c=s[p],v=a(c,p);n=ve(I,d,y,null,c,v,p,i,t,r),d.items.set(v,n),y=n}b>0&&P(G())}k||nr(s,d,o,i,t,a,r),f!==null&&(b===0?u?fe(u):u=q(()=>f(o)):u!==null&&Ie(u,()=>{u=null})),g&&V(!0),C(h)}),k&&(o=I)}function nr(e,t,r,a,i,f,o){var d=e.length,_=t.items,u=t.first,l=u,h,s=null,b=[],g=[],T,y,n,p;for(p=0;p<d;p+=1){if(T=e[p],y=f(T,p),n=_.get(y),n===void 0){var c=l?l.e.nodes_start:r;s=ve(c,t,s,s===null?t.first:s.next,T,y,p,a,i,o),_.set(y,s),b=[],g=[],l=s.next;continue}if(fr(n,T,p),(n.e.f&Z)!==0&&fe(n.e),n!==l){if(h!==void 0&&h.has(n)){if(b.length<g.length){var v=g[0],A;s=v.prev;var S=b[0],w=b[b.length-1];for(A=0;A<b.length;A+=1)X(b[A],v,r);for(A=0;A<g.length;A+=1)h.delete(g[A]);M(t,S.prev,w.next),M(t,s,S),M(t,w,v),l=v,s=w,p-=1,b=[],g=[]}else h.delete(n),X(n,l,r),M(t,n.prev,n.next),M(t,n,s===null?t.first:s.next),M(t,s,n),s=n;continue}for(b=[],g=[];l!==null&&l.k!==y;)(l.e.f&Z)===0&&(h??(h=new Set)).add(l),g.push(l),l=l.next;if(l===null)continue;n=l}b.push(n),s=n,l=n.next}if(l!==null||h!==void 0){for(var L=h===void 0?[]:oe(h);l!==null;)(l.e.f&Z)===0&&L.push(l),l=l.next;var H=L.length;if(H>0){var Ae=d===0?r:null;ir(t,L,Ae,_)}}F.first=t.first&&t.first.e,F.last=s&&s.e}function fr(e,t,r,a){Ne(e.v,t),e.i=r}function ve(e,t,r,a,i,f,o,d,_,u){var l=(_&$e)!==0,h=(_&ze)===0,s=l?h?B(i):J(i):i,b=(_&Oe)===0?o:J(o),g={i:b,v:s,k:f,a:null,e:null,prev:r,next:a};try{return g.e=q(()=>d(e,s,b,u),k),g.e.prev=r&&r.e,g.e.next=a&&a.e,r===null?t.first=g:(r.next=g,r.e.next=g.e),a!==null&&(a.prev=g,a.e.prev=g.e),g}finally{}}function X(e,t,r){for(var a=e.next?e.next.e.nodes_start:r,i=t?t.e.nodes_start:r,f=e.e.nodes_start;f!==a;){var o=Be(f);i.before(f),f=o}}function M(e,t,r){t===null?e.first=r:(t.next=r,t.e.next=r&&r.e),r!==null&&(r.prev=t,r.e.prev=t&&t.e)}function or(e,t){var r=void 0,a;ne(()=>{r!==(r=t())&&(a&&(le(a),a=null),r&&(a=q(()=>{je(()=>r(e))})))})}function de(e){var t,r,a="";if(typeof e=="string"||typeof e=="number")a+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=de(e[t]))&&(a&&(a+=" "),a+=r)}else for(r in e)e[r]&&(a&&(a+=" "),a+=r);return a}function lr(){for(var e,t,r=0,a="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=de(e))&&(a&&(a+=" "),a+=t);return a}function cr(e){return typeof e=="object"?lr(e):e??""}const W=[...` 	
\r\f \v\uFEFF`];function ur(e,t,r){var a=e==null?"":""+e;if(t&&(a=a?a+" "+t:t),r){for(var i in r)if(r[i])a=a?a+" "+i:i;else if(a.length)for(var f=i.length,o=0;(o=a.indexOf(i,o))>=0;){var d=o+f;(o===0||W.includes(a[o-1]))&&(d===a.length||W.includes(a[d]))?a=(o===0?"":a.substring(0,o))+a.substring(d+1):o=d}}return a===""?null:a}function x(e,t=!1){var r=t?" !important;":";",a="";for(var i in e){var f=e[i];f!=null&&f!==""&&(a+=" "+i+": "+f+r)}return a}function D(e){return e[0]!=="-"||e[1]!=="-"?e.toLowerCase():e}function vr(e,t){if(t){var r="",a,i;if(Array.isArray(t)?(a=t[0],i=t[1]):a=t,e){e=String(e).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var f=!1,o=0,d=!1,_=[];a&&_.push(...Object.keys(a).map(D)),i&&_.push(...Object.keys(i).map(D));var u=0,l=-1;const T=e.length;for(var h=0;h<T;h++){var s=e[h];if(d?s==="/"&&e[h-1]==="*"&&(d=!1):f?f===s&&(f=!1):s==="/"&&e[h+1]==="*"?d=!0:s==='"'||s==="'"?f=s:s==="("?o++:s===")"&&o--,!d&&f===!1&&o===0){if(s===":"&&l===-1)l=h;else if(s===";"||h===T-1){if(l!==-1){var b=D(e.substring(u,l).trim());if(!_.includes(b)){s!==";"&&h++;var g=e.substring(u,h).trim();r+=" "+g+";"}}u=h+1,l=-1}}}}return a&&(r+=x(a)),i&&(r+=x(i,!0)),r=r.trim(),r===""?null:r}return e==null?null:String(e)}function he(e,t,r,a,i,f){var o=e.__className;if(k||o!==r||o===void 0){var d=ur(r,a,f);(!k||d!==e.getAttribute("class"))&&(d==null?e.removeAttribute("class"):t?e.className=d:e.setAttribute("class",d)),e.__className=r}else if(f&&i!==f)for(var _ in f){var u=!!f[_];(i==null||u!==!!i[_])&&e.classList.toggle(_,u)}return f}function Y(e,t={},r,a){for(var i in r){var f=r[i];t[i]!==f&&(r[i]==null?e.style.removeProperty(i):e.style.setProperty(i,f,a))}}function dr(e,t,r,a){var i=e.__style;if(k||i!==t){var f=vr(t,a);(!k||f!==e.getAttribute("style"))&&(f==null?e.removeAttribute("style"):e.style.cssText=f),e.__style=t}else a&&(Array.isArray(a)?(Y(e,r==null?void 0:r[0],a[0]),Y(e,r==null?void 0:r[1],a[1],"important")):Y(e,r,a));return a}const R=Symbol("class"),O=Symbol("style"),ge=Symbol("is custom element"),pe=Symbol("is html");function hr(e,t){t?e.hasAttribute("selected")||e.setAttribute("selected",""):e.removeAttribute("selected")}function ee(e,t,r,a){var i=be(e);k&&(i[t]=e.getAttribute(t),t==="src"||t==="srcset"||t==="href"&&e.nodeName==="LINK")||i[t]!==(i[t]=r)&&(t==="loading"&&(e[De]=r),r==null?e.removeAttribute(t):typeof r!="string"&&_e(e).includes(t)?e[t]=r:e.setAttribute(t,r))}function re(e,t,r,a,i=!1){var f=be(e),o=f[ge],d=!f[pe];let _=k&&o;_&&V(!1);var u=t||{},l=e.tagName==="OPTION";for(var h in t)h in r||(r[h]=null);r.class?r.class=cr(r.class):r[R]&&(r.class=null),r[O]&&(r.style??(r.style=null));var s=_e(e);for(const c in r){let v=r[c];if(l&&c==="value"&&v==null){e.value=e.__value="",u[c]=v;continue}if(c==="class"){var b=e.namespaceURI==="http://www.w3.org/1999/xhtml";he(e,b,v,a,t==null?void 0:t[R],r[R]),u[c]=v,u[R]=r[R];continue}if(c==="style"){dr(e,v,t==null?void 0:t[O],r[O]),u[c]=v,u[O]=r[O];continue}var g=u[c];if(v!==g){u[c]=v;var T=c[0]+c[1];if(T!=="$$")if(T==="on"){const A={},S="$$"+c;let w=c.slice(2);var y=We(w);if(Je(w)&&(w=w.slice(0,-7),A.capture=!0),!y&&g){if(v!=null)continue;e.removeEventListener(w,u[S],A),u[S]=null}if(v!=null)if(y)e[`__${w}`]=v,Qe([w]);else{let L=function(H){u[c].call(this,H)};u[S]=Ke(w,e,L,A)}else y&&(e[`__${w}`]=void 0)}else if(c==="style")ee(e,c,v);else if(c==="autofocus")tr(e,!!v);else if(!o&&(c==="__value"||c==="value"&&v!=null))e.value=e.__value=v;else if(c==="selected"&&l)hr(e,v);else{var n=c;d||(n=Xe(n));var p=n==="defaultValue"||n==="defaultChecked";if(v==null&&!o&&!p)if(f[c]=null,n==="value"||n==="checked"){let A=e;const S=t===void 0;if(n==="value"){let w=A.defaultValue;A.removeAttribute(n),A.defaultValue=w,A.value=A.__value=S?w:null}else{let w=A.defaultChecked;A.removeAttribute(n),A.defaultChecked=w,A.checked=S?w:!1}}else e.removeAttribute(c);else p||s.includes(n)&&(o||typeof v!="string")?e[n]=v:typeof v!="function"&&ee(e,n,v)}}}_&&V(!0);for(let c of Object.getOwnPropertySymbols(r))c.description===He&&or(e,()=>r[c]);return u}function be(e){return e.__attributes??(e.__attributes={[ge]:e.nodeName.includes("-"),[pe]:e.namespaceURI===Pe})}var te=new Map;function _e(e){var t=te.get(e.nodeName);if(t)return t;te.set(e.nodeName,t=[]);for(var r,a=e,i=Element.prototype;i!==a;){r=Ye(a);for(var f in r)r[f].set&&t.push(f);a=Ze(a)}return t}const gr={default:{a:{fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},path:[{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"}]},micro:{a:{viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true"},path:[{d:"M8 1a2 2 0 0 0-2 2v4a2 2 0 1 0 4 0V3a2 2 0 0 0-2-2Z"},{d:"M4.5 7A.75.75 0 0 0 3 7a5.001 5.001 0 0 0 4.25 4.944V13.5h-1.5a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-1.5v-1.556A5.001 5.001 0 0 0 13 7a.75.75 0 0 0-1.5 0 3.5 3.5 0 1 1-7 0Z"}]},mini:{a:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},path:[{d:"M7 4a3 3 0 0 1 6 0v6a3 3 0 1 1-6 0V4Z"},{d:"M5.5 9.643a.75.75 0 0 0-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-1.5v-1.546A6.001 6.001 0 0 0 16 10v-.357a.75.75 0 0 0-1.5 0V10a4.5 4.5 0 0 1-9 0v-.357Z"}]},solid:{a:{viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},path:[{d:"M8.25 4.5a3.75 3.75 0 1 1 7.5 0v8.25a3.75 3.75 0 1 1-7.5 0V4.5Z"},{d:"M6 10.5a.75.75 0 0 1 .75.75v1.5a5.25 5.25 0 1 0 10.5 0v-1.5a.75.75 0 0 1 1.5 0v1.5a6.751 6.751 0 0 1-6 6.709v2.291h3a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3v-2.291a6.751 6.751 0 0 1-6-6.709v-1.5A.75.75 0 0 1 6 10.5Z"}]}};var pr=ae("<path></path>"),br=ae("<svg></svg>");function _r(e,t){const r=Q(t,["children","$$slots","$$events","$$legacy"]),a=Q(r,["src","size","solid","mini","micro"]);ce(t,!1);const i=B();let f=N(t,"src",8),o=N(t,"size",12,"100%"),d=N(t,"solid",8,!1),_=N(t,"mini",8,!1),u=N(t,"micro",8,!1);if(o()!=="100%"&&o().slice(-1)!="x"&&o().slice(-1)!="m"&&o().slice(-1)!="%")try{o(parseInt(o())+"px")}catch{o("100%")}qe(()=>(U(f()),U(d()),U(_()),U(u())),()=>{var s;E(i,(s=f())==null?void 0:s[d()?"solid":_()?"mini":u()?"micro":"default"])}),Ge(),se();var l=br();let h;sr(l,5,()=>{var s;return((s=C(i))==null?void 0:s.path)??[]},ar,(s,b)=>{var g=pr();let T;j(()=>T=re(g,T,{...C(b)})),z(s,g)}),m(l),j(()=>{var s;return h=re(l,h,{...(s=C(i))==null?void 0:s.a,xmlns:"http://www.w3.org/2000/svg",width:o(),height:o(),"aria-hidden":"true",...a})}),z(e,l),ue()}var Ar=ie('<p class="mt-4 text-red-400 svelte-32cf93"> </p>'),wr=ie('<main class="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center svelte-32cf93"><header class="text-center mb-12 svelte-32cf93"><h1 class="text-5xl font-bold text-blue-400 svelte-32cf93">AI Contact Center</h1> <p class="text-lg text-gray-300 mt-2 svelte-32cf93">ขับเคลื่อนด้วย AI เพื่อสร้างอนาคตที่ดีกว่า</p></header> <div class="relative w-24 h-24 flex items-center justify-center svelte-32cf93"><div role="button" tabindex="0"><!></div></div> <!> <footer class="mt-12 text-gray-500 text-sm text-center svelte-32cf93">© 2025 AI Contact Center</footer></main>');function Mr(e,t){ce(t,!1);let r=B("idle"),a=B(""),i;const f="https://rag.aivoices.work/ask",o="https://tts.aivoices.work/tts";we(()=>{i=new Audio("/tick.mp3")});function d(n){return/[฀-๿]/.test(n)?"th":/[一-鿿]/.test(n)?"zh":/[぀-ヿ]/.test(n)?"ja":/[가-힯]/.test(n)?"ko":(/^[a-zA-Z0-9\s.,?!'"-]+$/.test(n),"en")}async function _(){if(!("webkitSpeechRecognition"in window)){E(a,"เบราว์เซอร์ไม่รองรับการฟังเสียง");return}try{await i.play()}catch(p){console.warn("ไม่สามารถเล่นเสียง tick ได้:",p)}const n=new webkitSpeechRecognition;n.lang="th-TH",n.continuous=!1,n.interimResults=!1,E(r,"recording"),n.onresult=async p=>{const c=p.results[0][0].transcript;E(r,"processing");const v=d(c);await u(c,v)},n.onerror=p=>{E(a,`เกิดข้อผิดพลาด: ${p.error}`),E(r,"idle")},n.onend=()=>{C(r)==="recording"&&E(r,"idle")},n.start()}async function u(n,p){try{const v=await(await fetch(f,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({speak:n,lang:p})})).json();await l(v.answer,p)}catch(c){E(a,"ปัญหาในการส่งไป /ask: "+c),E(r,"idle")}}async function l(n,p){try{const v=await(await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({text:n,lang:p,slow:!1})})).blob(),A=URL.createObjectURL(v),S=new Audio(A);S.onended=()=>{URL.revokeObjectURL(A),E(r,"idle")},S.onerror=()=>{E(a,"เกิดข้อผิดพลาดขณะเล่นเสียง"),E(r,"idle"),URL.revokeObjectURL(A)},S.play()}catch(c){E(a,"เกิดข้อผิดพลาด TTS: "+c),E(r,"idle")}}se();var h=wr(),s=K($(h),2),b=$(s),g=$(b);_r(g,{src:gr,class:"w-16 h-16 text-white"}),m(b),m(s);var T=K(s,2);{var y=n=>{var p=Ar(),c=$(p);m(p),j(()=>er(c,`⚠️ ${C(a)??""}`)),z(n,p)};rr(T,n=>{C(a)&&n(y)})}Fe(2),m(h),j(()=>he(b,1,`p-6 rounded-full focus:outline-none focus:ring-4 transition-colors duration-200 z-20
        ${C(r)==="idle"?"bg-blue-500 hover:bg-blue-600 focus:ring-blue-400":C(r)==="recording"?"bg-red-500 hover:bg-red-600 focus:ring-red-400":C(r)==="processing"?"bg-orange-500 hover:bg-orange-600 focus:ring-orange-400":""}`,"svelte-32cf93")),xe("click",b,_),z(e,h),ue()}export{Mr as component};
