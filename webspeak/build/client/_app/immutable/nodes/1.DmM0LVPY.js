import{f as h,a as g}from"../chunks/DmJh76ia.js";import{i as v}from"../chunks/mPnEdIO8.js";import{q as l,p as d,t as x,s as _,v as e,w as o,x as $}from"../chunks/DETHqu2A.js";import{s as p}from"../chunks/BtLhCfhZ.js";import{s as k,p as m}from"../chunks/DgEu87TS.js";const b={get error(){return m.error},get status(){return m.status}};k.updated.check;const i=b;var q=h("<h1> </h1> <p> </p>",1);function A(f,n){l(n,!1),v();var t=q(),r=d(t),c=e(r,!0);o(r);var s=$(r,2),u=e(s,!0);o(s),x(()=>{var a;p(c,i.status),p(u,(a=i.error)==null?void 0:a.message)}),g(f,t),_()}export{A as component};
