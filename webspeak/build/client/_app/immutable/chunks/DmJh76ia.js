import{ag as l,a7 as u,ah as h,a1 as v,ai as g,aj as E,m as d,o,y as T,C as y}from"./DETHqu2A.js";function p(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function a(e,t){var r=v;r.nodes_start===null&&(r.nodes_start=e,r.nodes_end=t)}function M(e,t){var r=(t&g)!==0,_=(t&E)!==0,n,f=!e.startsWith("<!>");return()=>{if(d)return a(o,null),o;n===void 0&&(n=p(f?e:"<!>"+e),r||(n=u(n)));var s=_||h?document.importNode(n,!0):n.cloneNode(!0);if(r){var c=u(s),i=s.lastChild;a(c,i)}else a(s,s);return s}}function w(e,t,r="svg"){var _=!e.startsWith("<!>"),n=`<${r}>${_?e:"<!>"+e}</${r}>`,f;return()=>{if(d)return a(o,null),o;if(!f){var s=p(n),c=u(s);f=u(c)}var i=f.cloneNode(!0);return a(i,i),i}}function A(e,t){return w(e,t,"svg")}function C(e=""){if(!d){var t=l(e+"");return a(t,t),t}var r=o;return r.nodeType!==3&&(r.before(r=l()),y(r)),a(r,r),r}function L(){if(d)return a(o,null),o;var e=document.createDocumentFragment(),t=document.createComment(""),r=l();return e.append(t,r),a(t,r),e}function P(e,t){if(d){v.nodes_end=o,T();return}e!==null&&e.before(t)}const N="5";var m;typeof window<"u"&&((m=window.__svelte??(window.__svelte={})).v??(m.v=new Set)).add(N);export{P as a,a as b,L as c,A as d,M as f,C as t};
