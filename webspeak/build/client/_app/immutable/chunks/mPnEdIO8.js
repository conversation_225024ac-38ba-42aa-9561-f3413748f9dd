import{c as d,u as g,a as l,r as i,b,d as m,g as p,e as h,f as v,h as k}from"./DETHqu2A.js";function x(n=!1){const s=d,e=s.l.u;if(!e)return;let o=()=>h(s.s);if(n){let a=0,t={};const _=v(()=>{let c=!1;const r=s.s;for(const f in r)r[f]!==t[f]&&(t[f]=r[f],c=!0);return c&&a++,a});o=()=>p(_)}e.b.length&&g(()=>{u(s,o),i(e.b)}),l(()=>{const a=b(()=>e.m.map(m));return()=>{for(const t of a)typeof t=="function"&&t()}}),e.a.length&&l(()=>{u(s,o),i(e.a)})}function u(n,s){if(n.l.s)for(const e of n.l.s)p(e);s()}k();export{x as i};
