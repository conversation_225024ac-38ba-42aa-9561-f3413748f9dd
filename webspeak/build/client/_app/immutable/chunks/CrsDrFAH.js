import{i as F,m as h,y as G,E as H,z as K,H as Z,A as z,B as V,C as $,D as L,F as N,j as Y,G as g,U as j,o as J,I as Q,J as W,K as X,L as x,f as q,M as y,P as C,g as p,N as k,O as ee,Q as re,b as w,R as B,S as se,T as ne,V as ae,l as ie,W as ue,X as le,Y as fe,Z as te}from"./DETHqu2A.js";function de(e,r,[a,n]=[0,0]){h&&a===0&&G();var _=e,i=null,u=null,d=j,b=a>0?H:0,f=!1;const A=(l,t=!0)=>{f=!0,v(t,l)},v=(l,t)=>{if(d===(d=l))return;let I=!1;if(h&&n!==-1){if(a===0){const c=K(_);c===Z?n=0:c===z?n=1/0:(n=parseInt(c.substring(1)),n!==n&&(n=d?1/0:-1))}const S=n>a;!!d===S&&(_=V(),$(_),L(!1),I=!0,n=-1)}d?(i?N(i):t&&(i=Y(()=>t(_))),u&&g(u,()=>{u=null})):(u?N(u):t&&(u=Y(()=>t(_,[a+1,n]))),i&&g(i,()=>{i=null})),I&&L(!0)};F(()=>{f=!1,r(A),f||v(null,null)},b),h&&(_=J)}let R=!1;function ce(e){var r=R;try{return R=!1,[e(),R]}finally{R=r}}const _e={get(e,r){if(!e.exclude.includes(r))return p(e.version),r in e.special?e.special[r]():e.props[r]},set(e,r,a){return r in e.special||(e.special[r]=oe({get[r](){return e.props[r]}},r,C)),e.special[r](a),B(e.version),!0},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},deleteProperty(e,r){return e.exclude.includes(r)||(e.exclude.push(r),B(e.version)),!0},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function pe(e,r){return new Proxy({props:e,exclude:r,special:{},version:Q(0)},_e)}function U(e){var r;return((r=e.ctx)==null?void 0:r.d)??!1}function oe(e,r,a,n){var D;var _=(a&le)!==0,i=!ie||(a&ue)!==0,u=(a&se)!==0,d=(a&te)!==0,b=!1,f;u?[f,b]=ce(()=>e[r]):f=e[r];var A=ne in e||ae in e,v=u&&(((D=W(e,r))==null?void 0:D.set)??(A&&r in e&&(s=>e[r]=s)))||void 0,l=n,t=!0,I=!1,S=()=>(I=!0,t&&(t=!1,d?l=w(n):l=n),l);f===void 0&&n!==void 0&&(v&&i&&X(),f=S(),v&&v(f));var c;if(i)c=()=>{var s=e[r];return s===void 0?S():(t=!0,I=!1,s)};else{var O=(_?q:y)(()=>e[r]);O.f|=x,c=()=>{var s=p(O);return s!==void 0&&(l=void 0),s===void 0?l:s}}if((a&C)===0)return c;if(v){var M=e.$$legacy;return function(s,P){return arguments.length>0?((!i||!P||M||b)&&v(P?c():s),s):c()}}var E=!1,T=fe(f),o=q(()=>{var s=c(),P=p(T);return E?(E=!1,P):T.v=s});return u&&p(o),_||(o.equals=k),function(s,P){if(arguments.length>0){const m=P?p(o):i&&u?ee(s):s;if(!o.equals(m)){if(E=!0,re(T,m),I&&l!==void 0&&(l=m),U(o))return s;w(()=>p(o))}return s}return U(o)?o.v:p(o)}}export{de as i,pe as l,oe as p};
