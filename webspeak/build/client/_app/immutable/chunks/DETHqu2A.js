var un=Array.isArray,on=Array.prototype.indexOf,$n=Array.from,zn=Object.defineProperty,G=Object.getOwnPropertyDescriptor,_n=Object.getOwnPropertyDescriptors,cn=Object.prototype,vn=Array.prototype,Ft=Object.getPrototypeOf,kt=Object.isExtensible;const Wn=()=>{};function Xn(t){return t()}function Mt(t){for(var e=0;e<t.length;e++)t[e]()}const R=2,Lt=4,it=8,mt=16,k=32,B=64,nt=128,T=256,et=512,y=1024,I=2048,P=4096,j=8192,ut=16384,hn=32768,Yt=65536,Jn=1<<17,pn=1<<19,qt=1<<20,wt=1<<21,K=Symbol("$state"),Qn=Symbol("legacy props"),te=Symbol("");function jt(t){return t===this.v}function dn(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function Ht(t){return!dn(t,this.v)}function wn(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function yn(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function En(t){throw new Error("https://svelte.dev/e/effect_orphan")}function gn(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function ne(){throw new Error("https://svelte.dev/e/hydration_failed")}function ee(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function Tn(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function mn(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function An(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}let ot=!1;function re(){ot=!0}const ae=1,le=2,se=16,fe=1,ie=2,ue=4,oe=8,_e=16,ce=1,ve=2,xn="[",Rn="[!",bn="]",At={},E=Symbol(),he="http://www.w3.org/1999/xhtml",pe="@attach";let p=null;function Nt(t){p=t}function de(t,e=!1,n){var r=p={p,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};ot&&!e&&(p.l={s:null,u:null,r1:[],r2:Rt(!1)}),Nn(()=>{r.d=!0})}function we(t){const e=p;if(e!==null){const u=e.e;if(u!==null){var n=h,r=v;e.e=null;try{for(var a=0;a<u.length;a++){var l=u[a];lt(l.effect),H(l.reaction),zt(l.fn)}}finally{lt(n),H(r)}}p=e.p,e.m=!0}return{}}function _t(){return!ot||p!==null&&p.l===null}function q(t){if(typeof t!="object"||t===null||K in t)return t;const e=Ft(t);if(e!==cn&&e!==vn)return t;var n=new Map,r=un(t),a=N(0),l=v,u=i=>{var s=v;H(l);var f=i();return H(s),f};return r&&n.set("length",N(t.length)),new Proxy(t,{defineProperty(i,s,f){(!("value"in f)||f.configurable===!1||f.enumerable===!1||f.writable===!1)&&Tn();var _=n.get(s);return _===void 0?(_=u(()=>N(f.value)),n.set(s,_)):D(_,u(()=>q(f.value))),!0},deleteProperty(i,s){var f=n.get(s);if(f===void 0)s in i&&(n.set(s,u(()=>N(E))),dt(a));else{if(r&&typeof s=="string"){var _=n.get("length"),o=Number(s);Number.isInteger(o)&&o<_.v&&D(_,o)}D(f,E),dt(a)}return!0},get(i,s,f){var x;if(s===K)return t;var _=n.get(s),o=s in i;if(_===void 0&&(!o||(x=G(i,s))!=null&&x.writable)&&(_=u(()=>N(q(o?i[s]:E))),n.set(s,_)),_!==void 0){var c=C(_);return c===E?void 0:c}return Reflect.get(i,s,f)},getOwnPropertyDescriptor(i,s){var f=Reflect.getOwnPropertyDescriptor(i,s);if(f&&"value"in f){var _=n.get(s);_&&(f.value=C(_))}else if(f===void 0){var o=n.get(s),c=o==null?void 0:o.v;if(o!==void 0&&c!==E)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return f},has(i,s){var c;if(s===K)return!0;var f=n.get(s),_=f!==void 0&&f.v!==E||Reflect.has(i,s);if(f!==void 0||h!==null&&(!_||(c=G(i,s))!=null&&c.writable)){f===void 0&&(f=u(()=>N(_?q(i[s]):E)),n.set(s,f));var o=C(f);if(o===E)return!1}return _},set(i,s,f,_){var It;var o=n.get(s),c=s in i;if(r&&s==="length")for(var x=f;x<o.v;x+=1){var J=n.get(x+"");J!==void 0?D(J,E):x in i&&(J=u(()=>N(E)),n.set(x+"",J))}o===void 0?(!c||(It=G(i,s))!=null&&It.writable)&&(o=u(()=>N(void 0)),D(o,u(()=>q(f))),n.set(s,o)):(c=o.v!==E,D(o,u(()=>q(f))));var Q=Reflect.getOwnPropertyDescriptor(i,s);if(Q!=null&&Q.set&&Q.set.call(_,f),!c){if(r&&typeof s=="string"){var Ot=n.get("length"),pt=Number(s);Number.isInteger(pt)&&pt>=Ot.v&&D(Ot,pt+1)}dt(a)}return!0},ownKeys(i){C(a);var s=Reflect.ownKeys(i).filter(o=>{var c=n.get(o);return c===void 0||c.v!==E});for(var[f,_]of n)_.v!==E&&!(f in i)&&s.push(f);return s},setPrototypeOf(){mn()}})}function dt(t,e=1){D(t,t.v+e)}function xt(t){var e=R|I,n=v!==null&&(v.f&R)!==0?v:null;return h===null||n!==null&&(n.f&T)!==0?e|=T:h.f|=qt,{ctx:p,deps:null,effects:null,equals:jt,f:e,fn:t,reactions:null,rv:0,v:null,wv:0,parent:n??h}}function ye(t){const e=xt(t);return en(e),e}function Ee(t){const e=xt(t);return e.equals=Ht,e}function Bt(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)L(e[n])}}function Dn(t){for(var e=t.parent;e!==null;){if((e.f&R)===0)return e;e=e.parent}return null}function Ut(t){var e,n=h;lt(Dn(t));try{Bt(t),e=sn(t)}finally{lt(n)}return e}function Vt(t){var e=Ut(t),n=(S||(t.f&T)!==0)&&t.deps!==null?P:y;A(t,n),t.equals(e)||(t.v=e,t.wv=an())}const $=new Map;function Rt(t,e){var n={f:0,v:t,reactions:null,equals:jt,rv:0,wv:0};return n}function N(t,e){const n=Rt(t);return en(n),n}function ge(t,e=!1){var r;const n=Rt(t);return e||(n.equals=Ht),ot&&p!==null&&p.l!==null&&((r=p.l).s??(r.s=[])).push(n),n}function D(t,e,n=!1){v!==null&&!O&&_t()&&(v.f&(R|mt))!==0&&!(w!=null&&w.includes(t))&&An();let r=n?q(e):e;return On(t,r)}function On(t,e){if(!t.equals(e)){var n=t.v;X?$.set(t,e):$.set(t,n),t.v=e,(t.f&R)!==0&&((t.f&I)!==0&&Ut(t),A(t,(t.f&T)===0?y:P)),t.wv=an(),Gt(t,I),_t()&&h!==null&&(h.f&y)!==0&&(h.f&(k|B))===0&&(m===null?qn([t]):m.push(t))}return e}function Te(t,e=1){var n=C(t),r=e===1?n++:n--;return D(t,n),r}function Gt(t,e){var n=t.reactions;if(n!==null)for(var r=_t(),a=n.length,l=0;l<a;l++){var u=n[l],i=u.f;(i&I)===0&&(!r&&u===h||(A(u,e),(i&(y|T))!==0&&((i&R)!==0?Gt(u,P):ht(u))))}}function bt(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let M=!1;function me(t){M=t}let b;function z(t){if(t===null)throw bt(),At;return b=t}function Ae(){return z(Y(b))}function xe(t){if(M){if(Y(b)!==null)throw bt(),At;b=t}}function Re(t=1){if(M){for(var e=t,n=b;e--;)n=Y(n);b=n}}function be(){for(var t=0,e=b;;){if(e.nodeType===8){var n=e.data;if(n===bn){if(t===0)return e;t-=1}else(n===xn||n===Rn)&&(t+=1)}var r=Y(e);e.remove(),e=r}}function De(t){if(!t||t.nodeType!==8)throw bt(),At;return t.data}var St,In,Kt,Zt;function Oe(){if(St===void 0){St=window,In=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;Kt=G(e,"firstChild").get,Zt=G(e,"nextSibling").get,kt(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),kt(n)&&(n.__t=void 0)}}function yt(t=""){return document.createTextNode(t)}function Et(t){return Kt.call(t)}function Y(t){return Zt.call(t)}function Ie(t,e){if(!M)return Et(t);var n=Et(b);if(n===null)n=b.appendChild(yt());else if(e&&n.nodeType!==3){var r=yt();return n==null||n.before(r),z(r),r}return z(n),n}function ke(t,e){if(!M){var n=Et(t);return n instanceof Comment&&n.data===""?Y(n):n}return b}function Ne(t,e=1,n=!1){let r=M?b:t;for(var a;e--;)a=r,r=Y(r);if(!M)return r;var l=r==null?void 0:r.nodeType;if(n&&l!==3){var u=yt();return r===null?a==null||a.after(u):r.before(u),z(u),u}return z(r),r}function Se(t){t.textContent=""}function $t(t){h===null&&v===null&&En(),v!==null&&(v.f&T)!==0&&h===null&&yn(),X&&wn()}function kn(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function U(t,e,n,r=!0){var a=h,l={ctx:p,deps:null,nodes_start:null,nodes_end:null,f:t|I,first:null,fn:e,last:null,next:null,parent:a,prev:null,teardown:null,transitions:null,wv:0};if(n)try{vt(l),l.f|=hn}catch(s){throw L(l),s}else e!==null&&ht(l);var u=n&&l.deps===null&&l.first===null&&l.nodes_start===null&&l.teardown===null&&(l.f&(qt|nt))===0;if(!u&&r&&(a!==null&&kn(l,a),v!==null&&(v.f&R)!==0)){var i=v;(i.effects??(i.effects=[])).push(l)}return l}function Nn(t){const e=U(it,null,!1);return A(e,y),e.teardown=t,e}function Pe(t){$t();var e=h!==null&&(h.f&k)!==0&&p!==null&&!p.m;if(e){var n=p;(n.e??(n.e=[])).push({fn:t,effect:h,reaction:v})}else{var r=zt(t);return r}}function Ce(t){return $t(),Dt(t)}function Fe(t){const e=U(B,t,!0);return(n={})=>new Promise(r=>{n.outro?Fn(e,()=>{L(e),r(void 0)}):(L(e),r(void 0))})}function zt(t){return U(Lt,t,!1)}function Me(t,e){var n=p,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=Dt(()=>{t(),!r.ran&&(r.ran=!0,D(n.l.r2,!0),Kn(e))})}function Le(){var t=p;Dt(()=>{if(C(t.l.r2)){for(var e of t.l.r1){var n=e.effect;(n.f&y)!==0&&A(n,P),V(n)&&vt(n),e.ran=!1}t.l.r2.v=!1}})}function Dt(t){return U(it,t,!0)}function Ye(t,e=[],n=xt){const r=e.map(n);return Sn(()=>t(...r.map(C)))}function Sn(t,e=0){return U(it|mt|e,t,!0)}function qe(t,e=!0){return U(it|k,t,!0,e)}function Wt(t){var e=t.teardown;if(e!==null){const n=X,r=v;Pt(!0),H(null);try{e.call(null)}finally{Pt(n),H(r)}}}function Xt(t,e=!1){var n=t.first;for(t.first=t.last=null;n!==null;){var r=n.next;(n.f&B)!==0?n.parent=null:L(n,e),n=r}}function Pn(t){for(var e=t.first;e!==null;){var n=e.next;(e.f&k)===0&&L(e),e=n}}function L(t,e=!0){var n=!1;(e||(t.f&pn)!==0)&&t.nodes_start!==null&&(Cn(t.nodes_start,t.nodes_end),n=!0),Xt(t,e&&!n),ft(t,0),A(t,ut);var r=t.transitions;if(r!==null)for(const l of r)l.stop();Wt(t);var a=t.parent;a!==null&&a.first!==null&&Jt(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=null}function Cn(t,e){for(;t!==null;){var n=t===e?null:Y(t);t.remove(),t=n}}function Jt(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function Fn(t,e){var n=[];Qt(t,n,!0),Mn(n,()=>{L(t),e&&e()})}function Mn(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var a of t)a.out(r)}else e()}function Qt(t,e,n){if((t.f&j)===0){if(t.f^=j,t.transitions!==null)for(const u of t.transitions)(u.is_global||n)&&e.push(u);for(var r=t.first;r!==null;){var a=r.next,l=(r.f&Yt)!==0||(r.f&k)!==0;Qt(r,e,l?n:!1),r=a}}}function je(t){tn(t,!0)}function tn(t,e){if((t.f&j)!==0){t.f^=j,(t.f&y)===0&&(t.f^=y),V(t)&&(A(t,I),ht(t));for(var n=t.first;n!==null;){var r=n.next,a=(n.f&Yt)!==0||(n.f&k)!==0;tn(n,a?e:!1),n=r}if(t.transitions!==null)for(const l of t.transitions)(l.is_global||e)&&l.in()}}let W=[],gt=[];function nn(){var t=W;W=[],Mt(t)}function Ln(){var t=gt;gt=[],Mt(t)}function He(t){W.length===0&&queueMicrotask(nn),W.push(t)}function Yn(){W.length>0&&nn(),gt.length>0&&Ln()}let tt=!1,rt=!1,at=null,F=!1,X=!1;function Pt(t){X=t}let Z=[];let v=null,O=!1;function H(t){v=t}let h=null;function lt(t){h=t}let w=null;function en(t){v!==null&&v.f&wt&&(w===null?w=[t]:w.push(t))}let d=null,g=0,m=null;function qn(t){m=t}let rn=1,st=0,S=!1;function an(){return++rn}function V(t){var o;var e=t.f;if((e&I)!==0)return!0;if((e&P)!==0){var n=t.deps,r=(e&T)!==0;if(n!==null){var a,l,u=(e&et)!==0,i=r&&h!==null&&!S,s=n.length;if(u||i){var f=t,_=f.parent;for(a=0;a<s;a++)l=n[a],(u||!((o=l==null?void 0:l.reactions)!=null&&o.includes(f)))&&(l.reactions??(l.reactions=[])).push(f);u&&(f.f^=et),i&&_!==null&&(_.f&T)===0&&(f.f^=T)}for(a=0;a<s;a++)if(l=n[a],V(l)&&Vt(l),l.wv>t.wv)return!0}(!r||h!==null&&!S)&&A(t,y)}return!1}function jn(t,e){for(var n=e;n!==null;){if((n.f&nt)!==0)try{n.fn(t);return}catch{n.f^=nt}n=n.parent}throw tt=!1,t}function Ct(t){return(t.f&ut)===0&&(t.parent===null||(t.parent.f&nt)===0)}function ct(t,e,n,r){if(tt){if(n===null&&(tt=!1),Ct(e))throw t;return}if(n!==null&&(tt=!0),jn(t,e),Ct(e))throw t}function ln(t,e,n=!0){var r=t.reactions;if(r!==null)for(var a=0;a<r.length;a++){var l=r[a];w!=null&&w.includes(t)||((l.f&R)!==0?ln(l,e,!1):e===l&&(n?A(l,I):(l.f&y)!==0&&A(l,P),ht(l)))}}function sn(t){var x;var e=d,n=g,r=m,a=v,l=S,u=w,i=p,s=O,f=t.f;d=null,g=0,m=null,S=(f&T)!==0&&(O||!F||v===null),v=(f&(k|B))===0?t:null,w=null,Nt(t.ctx),O=!1,st++,t.f|=wt;try{var _=(0,t.fn)(),o=t.deps;if(d!==null){var c;if(ft(t,g),o!==null&&g>0)for(o.length=g+d.length,c=0;c<d.length;c++)o[g+c]=d[c];else t.deps=o=d;if(!S)for(c=g;c<o.length;c++)((x=o[c]).reactions??(x.reactions=[])).push(t)}else o!==null&&g<o.length&&(ft(t,g),o.length=g);if(_t()&&m!==null&&!O&&o!==null&&(t.f&(R|P|I))===0)for(c=0;c<m.length;c++)ln(m[c],t);return a!==null&&a!==t&&(st++,m!==null&&(r===null?r=m:r.push(...m))),_}finally{d=e,g=n,m=r,v=a,S=l,w=u,Nt(i),O=s,t.f^=wt}}function Hn(t,e){let n=e.reactions;if(n!==null){var r=on.call(n,t);if(r!==-1){var a=n.length-1;a===0?n=e.reactions=null:(n[r]=n[a],n.pop())}}n===null&&(e.f&R)!==0&&(d===null||!d.includes(e))&&(A(e,P),(e.f&(T|et))===0&&(e.f^=et),Bt(e),ft(e,0))}function ft(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)Hn(t,n[r])}function vt(t){var e=t.f;if((e&ut)===0){A(t,y);var n=h,r=p,a=F;h=t,F=!0;try{(e&mt)!==0?Pn(t):Xt(t),Wt(t);var l=sn(t);t.teardown=typeof l=="function"?l:null,t.wv=rn;var u=t.deps,i}catch(s){ct(s,t,n,r||t.ctx)}finally{F=a,h=n}}}function Bn(){try{gn()}catch(t){if(at!==null)ct(t,at,null);else throw t}}function fn(){var t=F;try{var e=0;for(F=!0;Z.length>0;){e++>1e3&&Bn();var n=Z,r=n.length;Z=[];for(var a=0;a<r;a++){var l=Vn(n[a]);Un(l)}$.clear()}}finally{rt=!1,F=t,at=null}}function Un(t){var e=t.length;if(e!==0)for(var n=0;n<e;n++){var r=t[n];if((r.f&(ut|j))===0)try{V(r)&&(vt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?Jt(r):r.fn=null))}catch(a){ct(a,r,null,r.ctx)}}}function ht(t){rt||(rt=!0,queueMicrotask(fn));for(var e=at=t;e.parent!==null;){e=e.parent;var n=e.f;if((n&(B|k))!==0){if((n&y)===0)return;e.f^=y}}Z.push(e)}function Vn(t){for(var e=[],n=t;n!==null;){var r=n.f,a=(r&(k|B))!==0,l=a&&(r&y)!==0;if(!l&&(r&j)===0){if((r&Lt)!==0)e.push(n);else if(a)n.f^=y;else try{V(n)&&vt(n)}catch(s){ct(s,n,null,n.ctx)}var u=n.first;if(u!==null){n=u;continue}}var i=n.parent;for(n=n.next;n===null&&i!==null;)n=i.next,i=i.parent}return e}function Gn(t){for(var e;;){if(Yn(),Z.length===0)return e;rt=!0,fn()}}async function Be(){await Promise.resolve(),Gn()}function C(t){var e=t.f,n=(e&R)!==0;if(v!==null&&!O){if(!(w!=null&&w.includes(t))){var r=v.deps;t.rv<st&&(t.rv=st,d===null&&r!==null&&r[g]===t?g++:d===null?d=[t]:(!S||!d.includes(t))&&d.push(t))}}else if(n&&t.deps===null&&t.effects===null){var a=t,l=a.parent;l!==null&&(l.f&T)===0&&(a.f^=T)}return n&&(a=t,V(a)&&Vt(a)),X&&$.has(t)?$.get(t):t.v}function Kn(t){var e=O;try{return O=!0,t()}finally{O=e}}const Zn=-7169;function A(t,e){t.f=t.f&Zn|e}function Ue(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(K in t)Tt(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&K in n&&Tt(n)}}}function Tt(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{Tt(t[r],e)}catch{}const n=Ft(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=_n(n);for(let a in r){const l=r[a].get;if(l)try{l.call(t)}catch{}}}}}export{lt as $,Rn as A,be as B,z as C,me as D,Yt as E,je as F,Fn as G,xn as H,Rt as I,G as J,ee as K,Jn as L,Ee as M,Ht as N,q as O,ue as P,D as Q,Te as R,oe as S,K as T,E as U,Qn as V,ie as W,fe as X,ge as Y,_e as Z,H as _,Pe as a,v as a0,h as a1,Nn as a2,He as a3,zn as a4,un as a5,Oe as a6,Et as a7,Y as a8,At as a9,te as aA,_n as aB,Me as aC,Le as aD,Re as aE,dn as aF,bn as aa,bt as ab,ne as ac,Se as ad,$n as ae,Fe as af,yt as ag,In as ah,ce as ai,ve as aj,zt as ak,Dt as al,Gn as am,N as an,Be as ao,ye as ap,j as aq,On as ar,le as as,Qt as at,Mn as au,ae as av,se as aw,pe as ax,he as ay,Ft as az,Kn as b,p as c,Xn as d,Ue as e,xt as f,C as g,re as h,Sn as i,qe as j,L as k,ot as l,M as m,Wn as n,b as o,ke as p,de as q,Mt as r,we as s,Ye as t,Ce as u,Ie as v,xe as w,Ne as x,Ae as y,De as z};
