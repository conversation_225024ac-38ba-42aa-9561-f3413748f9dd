import{_ as b,$ as m,a0 as I,a1 as N,a2 as M,a3 as j,a4 as H,a5 as W,a6 as k,a7 as q,H as B,a8 as Y,a9 as T,D as w,C as D,y as $,o as v,aa as z,ab as F,ac as G,ad as U,ae as x,af as J,ag as K,j as Q,q as X,c as Z,m as S,s as ee}from"./DETHqu2A.js";import{b as te}from"./DmJh76ia.js";function de(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const re=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function fe(e){return re.includes(e)}const ae={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function _e(e){return e=e.toLowerCase(),ae[e]??e}const oe=["touchstart","touchmove"];function ne(e){return oe.includes(e)}function ie(e){var t=I,a=N;b(null),m(null);try{return e()}finally{b(t),m(a)}}const O=new Set,L=new Set;function se(e,t,a,s={}){function n(r){if(s.capture||y.call(t,r),!r.cancelBubble)return ie(()=>a==null?void 0:a.call(this,r))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?j(()=>{t.addEventListener(e,n,s)}):t.addEventListener(e,n,s),n}function pe(e,t,a,s,n){var r={capture:s,passive:n},u=se(e,t,a,r);(t===document.body||t===window||t===document)&&M(()=>{t.removeEventListener(e,u,r)})}function he(e){for(var t=0;t<e.length;t++)O.add(e[t]);for(var a of L)a(e)}function y(e){var R;var t=this,a=t.ownerDocument,s=e.type,n=((R=e.composedPath)==null?void 0:R.call(e))||[],r=n[0]||e.target,u=0,_=e.__root;if(_){var d=n.indexOf(_);if(d!==-1&&(t===document||t===window)){e.__root=t;return}var p=n.indexOf(t);if(p===-1)return;d<=p&&(u=d)}if(r=n[u]||e.target,r!==t){H(e,"currentTarget",{configurable:!0,get(){return r||a}});var E=I,c=N;b(null),m(null);try{for(var o,i=[];r!==null;){var l=r.assignedSlot||r.parentNode||r.host||null;try{var f=r["__"+s];if(f!=null&&(!r.disabled||e.target===r))if(W(f)){var[P,...C]=f;P.apply(r,[e,...C])}else f.call(r,e)}catch(g){o?i.push(g):o=g}if(e.cancelBubble||l===t||l===null)break;r=l}if(o){for(let g of i)queueMicrotask(()=>{throw g});throw o}}finally{e.__root=t,delete e.currentTarget,b(E),m(c)}}}function ve(e,t){var a=t==null?"":typeof t=="object"?t+"":t;a!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=a,e.nodeValue=a+"")}function ue(e,t){return V(e,t)}function ye(e,t){k(),t.intro=t.intro??!1;const a=t.target,s=S,n=v;try{for(var r=q(a);r&&(r.nodeType!==8||r.data!==B);)r=Y(r);if(!r)throw T;w(!0),D(r),$();const u=V(e,{...t,anchor:r});if(v===null||v.nodeType!==8||v.data!==z)throw F(),T;return w(!1),u}catch(u){if(u===T)return t.recover===!1&&G(),k(),U(a),w(!1),ue(e,t);throw u}finally{w(s),D(n)}}const h=new Map;function V(e,{target:t,anchor:a,props:s={},events:n,context:r,intro:u=!0}){k();var _=new Set,d=c=>{for(var o=0;o<c.length;o++){var i=c[o];if(!_.has(i)){_.add(i);var l=ne(i);t.addEventListener(i,y,{passive:l});var f=h.get(i);f===void 0?(document.addEventListener(i,y,{passive:l}),h.set(i,1)):h.set(i,f+1)}}};d(x(O)),L.add(d);var p=void 0,E=J(()=>{var c=a??t.appendChild(K());return Q(()=>{if(r){X({});var o=Z;o.c=r}n&&(s.$$events=n),S&&te(c,null),p=e(c,s)||{},S&&(N.nodes_end=v),r&&ee()}),()=>{var l;for(var o of _){t.removeEventListener(o,y);var i=h.get(o);--i===0?(document.removeEventListener(o,y),h.delete(o)):h.set(o,i)}L.delete(d),c!==a&&((l=c.parentNode)==null||l.removeChild(c))}});return A.set(p,E),p}let A=new WeakMap;function ge(e,t){const a=A.get(e);return a?(A.delete(e),a(t)):Promise.resolve()}export{fe as a,se as c,he as d,pe as e,ye as h,de as i,ue as m,_e as n,ve as s,ge as u};
